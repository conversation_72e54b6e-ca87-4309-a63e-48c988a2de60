package com.ybmmarket20.adapter

import android.content.Context
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybmmarket20.utils.analysis.BaseFlowData

abstract class YBMBaseListAdapter<T>(layoutResId: Int, data: MutableList<T>?) :
    YBMBaseAdapter<T>(layoutResId, data) {

    constructor(data: MutableList<T>?): this(0, data)

    open var flowData: BaseFlowData? = null

    open fun setBaseFlowData(fd: BaseFlowData?) {
        flowData = fd
    }

    open fun setContext(context: Context) {
        mContext = context
    }

}