package com.ybmmarket20.activity;

import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.util.constant.RegexConstants;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.RegexUtil;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 设置密码
 */
public class SetPasswordActivity extends BaseActivity {
    @Bind(R.id.new_password_iv03)
    ImageView newPasswordIv03;
    @Bind(R.id.textView3)
    TextView textView3;
    @Bind(R.id.new_password_et03)
    EditText newPasswordEt03;
    @Bind(R.id.new_password_rl03)
    RelativeLayout newPasswordRl03;
    @Bind(R.id.new_password_iv)
    ImageView newPasswordIv;
    @Bind(R.id.textView1)
    TextView textView1;
    @Bind(R.id.new_password_et01)
    EditText newPasswordEt01;
    @Bind(R.id.new_password_rl01)
    RelativeLayout newPasswordRl01;
    @Bind(R.id.new_password_iv02)
    ImageView newPasswordIv02;
    @Bind(R.id.textView2)
    TextView textView2;
    @Bind(R.id.new_password_et02)
    EditText newPasswordEt02;
    @Bind(R.id.new_password_rl02)
    RelativeLayout newPasswordRl02;
    @Bind(R.id.new_password_btn)
    Button newPasswordBtn;
    private String phone;
    private String verificationCode;

    @Override
    protected void initData() {
        setTitle("设置密码");
        phone = getIntent().getStringExtra(IntentCanst.ACTION_NEW_PHONE);
        verificationCode = getIntent().getStringExtra(IntentCanst.ACTION_VERIFICATIONCODE);
        editNewPassword();
    }

    private void editNewPassword() {
        newPasswordEt03.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String et = newPasswordEt03.getText().toString();
                if (!TextUtils.isEmpty(et)) {
                    newPasswordBtn.setBackgroundResource(R.drawable.round_corner_bg);
                    newPasswordBtn.setTextColor(SetPasswordActivity.this.getResources().getColor(R.color.white));
                } else {
                    newPasswordBtn.setBackgroundResource(R.drawable.round_corner_gay_bg);
                    newPasswordBtn.setTextColor(SetPasswordActivity.this.getResources().getColor(R.color.login_btn_tv));
                }
            }
        });
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_set_password;
    }

    @OnClick({R.id.new_password_btn})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.new_password_btn:
                String old_password = newPasswordEt03.getText().toString().trim();
                String new_password = newPasswordEt01.getText().toString().trim();
                String affirm_password = newPasswordEt02.getText().toString().trim();

                if (TextUtils.isEmpty(old_password) || TextUtils.isEmpty(new_password) || TextUtils.isEmpty(affirm_password)) {

                    ToastUtils.showShort("密码不能为空");
                    return;
                }
                //密码长度大于8位
                if (new_password.trim().length() < 8) {
                    ToastUtils.showShort(R.string.validate_pwd_error);
                    return;
                }

                if (!new_password.equals(affirm_password)) {
                    ToastUtils.showShort("请确保两次输入的密码一致");
                    return;
                }
                //校验新密码规则是否正确
                if (!TextUtils.isEmpty(new_password) && !RegexUtil.Companion.getInstance().match(new_password, RegexConstants.REGEX_PWD_SERVER)) {
                    ToastUtils.showShort(R.string.validate_pwd_rule);
                    return;
                }

                if (!TextUtils.isEmpty(phone) && !TextUtils.isEmpty(verificationCode)) {

                    postPasswordResponse(old_password, new_password, phone, verificationCode);
                } else {
                    ToastUtils.showShort("数据异常，请重新修改密码!");
                }
                break;
        }
    }

    /**
     * 确认修改密码
     * @param old_password     旧密码
     * @param new_password     新密码
     * @param phone            电话
     * @param verificationCode
     */
    private void postPasswordResponse(String old_password, String new_password, String phone, String verificationCode) {
        showProgress();
        newPasswordBtn.setEnabled(false);

        RequestParams params = new RequestParams();
        params.put("mobileNumber", phone);
        params.put("oldPassword", old_password);
        params.put("newPassword", new_password);
        params.put("code", verificationCode);
        HttpManager.getInstance().post(AppNetConfig.PASSWORD_AFFIRM, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                newPasswordBtn.setEnabled(true);
                dismissProgress();
                if (obj != null) {
                    if (obj.isSuccess()) {
//                        清除登录信息
                        loginOut();
                        ToastUtils.showShort("密码修改成功，请重新登录");
                        Intent intent = new Intent(SetPasswordActivity.this, LoginActivity.class);
                        startActivity(intent);
                        finish();
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                newPasswordBtn.setEnabled(true);
                dismissProgress();
            }
        });
    }

}
