package com.ybmmarket20.activity

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ybmmarket20.activity.jdpay.PayWayBaseV2Activity
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.xyyreport.page.payment.PaymentReport
import com.ybmmarket20.xyyreport.page.payment.PaymentReportConstant
import com.ybmmarket20.xyyreport.spm.ISpm
import com.ybmmarket20.xyyreport.spm.SpmUtil

abstract class PaymentAnalysisActivity: PayWayBaseV2Activity() {

    override fun initData() {
        PaymentReport.pvTrack(this)
    }

    fun trackSubmitOrderClick(text: String?) {
        PaymentReport.trackSubmitOrderClick(this, text)
    }

    fun trackOrderConfirmParams(context: Context, requestParams: RequestParams?) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getExtensionValue(PaymentReportConstant.PAYMENT_REPORT_ORDER_CONFIRM_CLICK_SPM)
            val scm = it.getExtensionValue(PaymentReportConstant.PAYMENT_REPORT_ORDER_CONFIRM_CLICK_SCM)
            if (spm == null || scm == null) return@checkAnalysisContext
            val spmCnt = spm as ISpm
            val scmCnt = scm as ISpm
            val map = mutableMapOf<String, Any>(
                "spm_cnt" to spmCnt.concat(),
                "scm_cnt" to scmCnt.concat(),
            )
            val qtData = Gson().toJson(map)
            requestParams?.put("qtdata", qtData)
        }
    }
}