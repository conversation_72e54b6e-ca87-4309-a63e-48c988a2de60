package com.ybmmarket20.home.newpage

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.view.View
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.analysys.ANSAutoPageTracker
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.home.BrandFragment
import com.ybmmarketkotlin.utils.RouterJump
import kotlinx.android.synthetic.main.activity_all_drug.iv_back
import kotlinx.android.synthetic.main.activity_all_drug.iv_search
import kotlinx.android.synthetic.main.activity_all_drug.iv_shop_car

/**
 * <AUTHOR>
 * @date 2024-04-09
 * @description 全部药品Activity
 */
class AllDrugActivity: BaseActivity(),ANSAutoPageTracker {


    private lateinit var tvNum: TextView

    override fun getContentViewId(): Int = R.layout.activity_all_drug


    companion object {

        fun launchActivity(mContext: Context) {
            mContext.startActivity(Intent(mContext, AllDrugActivity::class.java))
        }
    }

    override fun initData() {
        initObserver()
        initView()
        getCartNumber()
        initBroadCastReceiver()
    }

    private fun initBroadCastReceiver() {
        br = object : BroadcastReceiver() {
            override fun onReceive(
                    context: Context,
                    intent: Intent
            ) {
                if (intent != null) {
                    if (IntentCanst.CART_NUM_CHANGED == intent.action) { //购物车数量修改了
                        getCartNumber()
                    }
                }
            }
        }
        val intentFilter = IntentFilter()
        intentFilter.addAction(IntentCanst.CART_NUM_CHANGED)
        LocalBroadcastManager.getInstance(applicationContext).registerReceiver(br, intentFilter)
    }

    fun initView() {
        supportFragmentManager.beginTransaction().add(R.id.fl_container, BrandFragment()).commit()

        tvNum = findViewById(R.id.tv_num)
    }

    fun initObserver() {
        iv_back.setOnClickListener {
            finish()
        }

        iv_search.setOnClickListener {
            RouterJump.jump2SearchOPPage("全部药品(搜索按钮)")
        }

        iv_shop_car.setOnClickListener {
            RouterJump.jump2ShopCar()
        }
    }

    private fun getCartNumber() {
        val num = YBMAppLike.cartNum
        if (num > 0) {
            if (num > 99) {
                tvNum.text = 99.toString() + "+"
            } else {
                tvNum.text = num.toString() + ""
            }
            tvNum.visibility = View.VISIBLE
        } else {
            tvNum.text = ""
            tvNum.visibility = View.GONE
        }
    }

    override fun registerPageProperties(): MutableMap<String, Any> {
        val properties: MutableMap<String, Any> = HashMap()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackAllDrugs.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackAllDrugs.TITLE
        return properties
    }

    override fun registerPageUrl(): String = this.getFullClassName()
}