//package com.ybmmarket20.home;
//
//import android.annotation.SuppressLint;
//import android.content.BroadcastReceiver;
//import android.content.Context;
//import android.content.Intent;
//import android.content.IntentFilter;
//import android.net.Uri;
//import android.text.Html;
//import android.text.TextUtils;
//import android.view.Gravity;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.RelativeLayout;
//import android.widget.TextView;
//
//import androidx.core.widget.NestedScrollView;
//import androidx.lifecycle.ViewModelProvider;
//import androidx.localbroadcastmanager.content.LocalBroadcastManager;
//import androidx.recyclerview.widget.GridLayoutManager;
//import androidx.recyclerview.widget.RecyclerView;
//
//import com.google.gson.reflect.TypeToken;
//import com.ybm.app.bean.NetError;
//import com.ybm.app.common.BaseYBMApp;
//import com.ybm.app.view.WrapLinearLayoutManager;
//import com.ybmmarket20.BuildConfig;
//import com.ybmmarket20.R;
//import com.ybmmarket20.activity.CouponMemberActivity;
//import com.ybmmarket20.activity.ElsePageActivity;
//import com.ybmmarket20.activity.MemberSignActivity;
//import com.ybmmarket20.activity.PersonalHelpActivity;
//import com.ybmmarket20.adapter.CommonToolsAdapter;
//import com.ybmmarket20.bean.BaseBean;
//import com.ybmmarket20.bean.BoothData;
//import com.ybmmarket20.bean.BoothDetail;
//import com.ybmmarket20.bean.EmptyBean;
//import com.ybmmarket20.bean.GiftSkulistBean;
//import com.ybmmarket20.bean.ImPackUrlBean;
//import com.ybmmarket20.bean.InfoByMerchantIdBean;
//import com.ybmmarket20.bean.LoginMerchantInfo;
//import com.ybmmarket20.bean.MerchantInfo;
//import com.ybmmarket20.bean.OnlinServiceInfoBean;
//import com.ybmmarket20.bean.OrderStatusNumber;
//import com.ybmmarket20.bean.RefreshWrapperPagerBean;
//import com.ybmmarket20.bean.RowsBean;
//import com.ybmmarket20.bean.SalesInfoBean;
//import com.ybmmarket20.bean.UseToolsBean;
//import com.ybmmarket20.business.order.ui.OrderListActivity;
//import com.ybmmarket20.common.AdDialog3;
//import com.ybmmarket20.common.AlertDialogEx;
//import com.ybmmarket20.common.BaseActivity;
//import com.ybmmarket20.common.BaseFragment;
//import com.ybmmarket20.common.BaseResponse;
//import com.ybmmarket20.common.RequestParams;
//import com.ybmmarket20.common.YBMAppLike;
//import com.ybmmarket20.common.util.ConvertUtils;
//import com.ybmmarket20.common.util.TextColorChangeUtils;
//import com.ybmmarket20.common.util.ToastUtils;
//import com.ybmmarket20.constant.AppNetConfig;
//import com.ybmmarket20.constant.ConstantData;
//import com.ybmmarket20.constant.IntentCanst;
//import com.ybmmarket20.constant.SpConstantKt;
//import com.ybmmarket20.db.AccountTable;
//import com.ybmmarket20.message.Message;
//import com.ybmmarket20.network.HttpManager;
//import com.ybmmarket20.utils.AdapterUtils;
//import com.ybmmarket20.utils.PushUtil;
//import com.ybmmarket20.utils.RoutersUtils;
//import com.ybmmarket20.utils.SpUtil;
//import com.ybmmarket20.utils.StatusBarUtils;
//import com.ybmmarket20.utils.UiUtils;
//import com.ybmmarket20.utils.YBMPayUtil;
//import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
//import com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt;
//import com.ybmmarket20.utils.analysis.XyyIoUtil;
//import com.ybmmarket20.view.cms.MarqueeViewCms;
//import com.ybmmarket20.view.homesteady.HomeSteadyBannerView;
//import com.ybmmarket20.view.homesteady.HomeSteadyStreamerView;
//import com.ybmmarket20.viewmodel.SearchDataViewModel;
//import com.ybmmarketkotlin.adapter.GoodListAdapterNew;
//import com.ybmmarketkotlin.bean.ApplyNoticeBean;
//import com.ybmmarketkotlin.viewmodel.PayNoticeViewModel;
//import com.ybmmarketkotlin.views.MarqueeViewKt;
//
//import java.lang.reflect.Type;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import butterknife.Bind;
//import butterknife.ButterKnife;
//import butterknife.OnClick;
//
///**
// * 我的
// */
//public class MineFragment extends BaseFragment {
//
//    @Bind(R.id.ll_title)
//    RelativeLayout llTitle;
//    @Bind(R.id.ll_base_info)
//    RelativeLayout llBaseInfo;
//    @Bind(R.id.title_right)
//    ImageView titleRight;
//    @Bind(R.id.textView_shop)
//    TextView textViewShop;
//    @Bind(R.id.tv_account_info)
//    TextView tvAccountInfo;
//    @Bind(R.id.prompt_popup)
//    View promptPopup;
//    @Bind(R.id.sc)
//    NestedScrollView sc;
//    @Bind(R.id.favorable_tv)
//    TextView favorableTv;
//    @Bind(R.id.favorable_iv)
//    ImageView favorableIv;
//    @Bind(R.id.rl_order_form)
//    RelativeLayout rlOrderForm;
//    @Bind(R.id.activity_common_shoucang_iv)
//    ImageView activityCommonShoucangIv;
//    @Bind(R.id.detail_wait_ll)
//    LinearLayout detailWaitLl;
//    @Bind(R.id.detail_process_ll)
//    LinearLayout detailProcessLl;
//    @Bind(R.id.detail_finish_ll)
//    LinearLayout detailFinishLl;
//    @Bind(R.id.ll_kefu_iv)
//    ImageView llKefuIv;
//    @Bind(R.id.ll_kefu_tv1)
//    TextView llKefuTv1;
//    @Bind(R.id.ll_kefu)
//    RelativeLayout llKefu;
//    @Bind(R.id.more_ll)
//    RelativeLayout moreLl;
//    @Bind(R.id.balance_tv)
//    TextView balance_tv;
//    @Bind(R.id.coupon_tv)
//    TextView coupon_tv;
//    @Bind(R.id.tv_smg_num)
//    TextView tvSmgNum;
//    @Bind(R.id.tv_smg_num_more)
//    TextView tvSmgNumMore;
//    @Bind(R.id.tv_smg_wait_pay)
//    TextView tvSmgWaitPay;
//    @Bind(R.id.tv_smg_wait_deliver)
//    TextView tvSmgWaitDeliver;
//    @Bind(R.id.tv_smg_wait_receive)
//    TextView tvSmgWaitReceive;
//    @Bind(R.id.tv_smg_wait_balance)
//    TextView tvSmgWaitBalance;
//    @Bind(R.id.tv_smg_wait_service)
//    TextView tvSmgWaitService;
//    @Bind(R.id.tv_activity_matter)
//    TextView tvActivityMatter;
//    @Bind(R.id.tv_balance)
//    TextView tvBalance;
//    //大礼包
//    @Bind(R.id.iv_vip_gift)
//    ImageView ivVipGift;
//    @Bind(R.id.iv_vip_gift_close)
//    ImageView ivVipGiftClose;
//    @Bind(R.id.rl_mine_gift_pop)
//    RelativeLayout rlMineGiftPop;
//    @Bind(R.id.ll_aptitude_download)
//    LinearLayout llAptitudeDownload;//小药药资质
//    @Bind(R.id.tv_my_banking)
//    TextView tvMyBanking;//我的金融
//    @Bind(R.id.title_tv)
//    TextView tvTitle;//我的
//
//    @Bind(R.id.rl_common_tools)
//    RecyclerView rl_common_tools;
//    @Bind(R.id.crv_recommend)
//    RecyclerView crvRecommend;
//    @Bind(R.id.tv_my_service_num)
//    TextView tvMyServiceNum;
//    @Bind(R.id.hsbv_minebanner)
//    HomeSteadyBannerView mineBanner;
//    @Bind(R.id.hssv_minestreamer)
//    HomeSteadyStreamerView mineStreamer;
//
//    @Bind(R.id.ll_marqueue_pay_notice)
//    LinearLayout llMarqueuePayNotice;
//    @Bind(R.id.marquee_pay_notice)
//    MarqueeViewCms marqueePayNotice;
//    @Bind(R.id.tv_red_envelope)
//    TextView tvRedEnvelope;
//    @Bind(R.id.ll_virtual_money)
//    LinearLayout llVirtualMoney;
//    @Bind(R.id.tv_virtual_money)
//    TextView tvVirtualMoney;
//
//    private ArrayList<UseToolsBean> commonToolsData = new ArrayList<>();
//    private CommonToolsAdapter commonToolsAdapter;
//
//    GiftSkulistBean bean;
//
//    private int isBlack;//是否白名单用户
//    private int iousIsOpen;//是否湖北域 1 开通  0 未开通
//    private String creditExtensionUrl;//小药白条h5地址
//
//    private static final int WHITE_LIST_USER = 1;//白名单用户
//    private static final int IOUS_IS_OPEN = 1;//湖北域 开通
//    private String name;
//    private String shopName;
//    private String phone;
//    private List<RowsBean> recommendList = new ArrayList<>();
//    private GoodListAdapterNew recommendAdapter = new GoodListAdapterNew(R.layout.item_goods_new, recommendList, false);
//    private View footerView;
//    private int loadMoreStatus = LOAD_MORE_STATUS_DEFAULT;
//    public static final int LOAD_MORE_STATUS_DEFAULT = 0;
//    public static final int LOAD_MORE_STATUS_LOADING = 1;
//    public static final int LOAD_MORE_STATUS_NO_MORE = 2;
//
//    private Map<String, String> mParamsMap;
//    private SearchDataViewModel mViewModel;
//    private String redEnvelopGold;
//
//    private BroadcastReceiver mInfoAndMsgReceiver = new BroadcastReceiver() {
//        @Override
//        public void onReceive(Context context, Intent intent) {
//            String action = intent.getAction();
//            if (TextUtils.isEmpty(action)) {
//                return;
//            }
//            if (IntentCanst.ACTION_MERCHANTBASEINFO.equals(action)) {
//                initData(null);
//            } else if (IntentCanst.ACTION_ORDER_STATUS.equals(action)) {
//                getOderStatus();
//            } else if (IntentCanst.ACTION_SWITCH_USER.equals(action)) {
//                refreshUserInfo();
//            }
//        }
//    };
//
//    private void refreshUnpayStatus() {
//        if (payNoticeViewModel != null) payNoticeViewModel.getPayNotice(SpUtil.getMerchantid());
//    }
//
//    private boolean isFirst;
//
//
//    private void initView() {
//        // 我的- 常用工具
//        commonToolsAdapter = new CommonToolsAdapter(R.layout.item_common_tool, commonToolsData);
//        rl_common_tools.setLayoutManager(new GridLayoutManager(getContext(), 4));
//        rl_common_tools.setAdapter(commonToolsAdapter);
//        crvRecommend.setLayoutManager(new WrapLinearLayoutManager(getContext(), WrapLinearLayoutManager.VERTICAL, false));
//        crvRecommend.setNestedScrollingEnabled(false);
//        crvRecommend.setAdapter(recommendAdapter);
//        recommendAdapter.setEnableLoadMore(true);
//
//        if (BuildConfig.DEBUG) {
//
//            textViewShop.setOnClickListener(v ->
//                    {
////                        FlutterUtils.INSTANCE.openPage(getNotNullActivity(),"/Setting");
//
////                        String action = "ybmpage://searchproduct?keyword=6999999032413242314";
////                        RoutersUtils.open(action);
//
////                        RoutersUtils.open("ybmpage://login_vertification");
////                        RoutersUtils.open("ybmpage://YBMCollectOrdersVC");
//
//
//                        /*String encode = ChCrypto.INSTANCE.aesEncrypt("18999999997");
//                        LogUtils.e("encode = " + encode);
//                        String decode = ChCrypto.INSTANCE.aesDecrypt(encode);
//                        LogUtils.e("decode = " + decode);*/
//
////                        RoutersUtils.open("ybmpage://paywayactivity?orderId=7656276" + "&amount=1130" + "&orderNo=YBM20210826135553125776" + "&payRoute=0");
//                        RoutersUtils.open("ybmpage://payresultactivity/" + "6202780" + "/" + YBMPayUtil.PAY_TRAN + "/" + "0.01" + "/" + "YBM20210706234024100038");
////                        RoutersUtils.open("ybmpage://payresultactivity/" + "6203256" + "/" + YBMPayUtil.PAY_TRAN + "/" + "0.01" + "/" + "YBM20210805210421100019");
//
////                        refreshUnpayStatus();
//
//                    }
//
//
//            );
//        }
//
//
//    }
//
//    @Override
//    public void onResume() {
//        super.onResume();
//        if (isFirst && SpUtil.readBoolean("isVerificationAlert", true)) {
//            getInfoByMerchantId();
//        }
//        //activity回到此面需要刷新状态
//        refreshUserInfo();
//        isFirst = false;
//    }
//
//    // 刷新用户信息
//    private void refreshUserInfo() {
//        getOderStatus();
//        getVipGift();
//        getMerchantBaseInfo(isFirst);
//        getUseTools();
//        getOnlineServiceInfoCount();
//        Message.instance.reInit();
//        getBoothData();
//    }
//
//    @Override
//    protected void initData(String content) {
//
//        initReceiver();
//        if (SpUtil.readBoolean("isVerificationAlert", true)) {
//            getInfoByMerchantId();
//        }
//
//        if (YBMAppLike.isNewTheme) {
//            YBMAppLike.changeThemeBg(R.drawable.base_header_dynamic_bg, llTitle);
//            llBaseInfo.setBackgroundResource(R.drawable.bg_merchant_info);
//        }
//        isFirst = true;
//        Message.instance.bindUnreadMsgCount(listener);
//        //关闭按钮
//        promptPopup.findViewById(R.id.bt_close).setOnClickListener(v -> promptPopup.setVisibility(View.GONE));
//        setAnimation();
//        initView();
//
//        initPayNotice();
//        mViewModel = new ViewModelProvider(this).get(SearchDataViewModel.class);
//        Map<String, String> paramsMap = new HashMap<>();
//        paramsMap.put("sptype", "3"); //推荐页面
//        paramsMap.put("pageType", "7"); //我的个人中心页内推荐
//        mViewModel.getRecommendLiveData().observe(this, pagerBeanOrg -> {
//            RefreshWrapperPagerBean<RowsBean> pagerBean = pagerBeanOrg.data;
//            if(pagerBean == null || pagerBean.getRows() == null){
//                // 兜底，防止崩溃
//                return;
//            }
//
//            mParamsMap = pagerBean.getRequestParam();
//            recommendAdapter.removeFooterView(footerView);
//            recommendList.addAll(pagerBean.getRows());
//            // 请求并更新折后价
//            AdapterUtils.INSTANCE.getAfterDiscountPrice(pagerBean.getRows(), recommendAdapter);
//            recommendAdapter.notifyDataSetChanged();
//            recommendAdapter.notifyDataChangedAfterLoadMore(true);
//            if (pagerBean.isEnd()) {
//                recommendAdapter.addFooterView(View.inflate(getContext(), R.layout.not_loading, null));
//                loadMoreStatus = LOAD_MORE_STATUS_NO_MORE;
//            } else {
//                if (footerView == null) {
//                    footerView = View.inflate(getContext(), R.layout.view_loadmore, null);
//                }
//                recommendAdapter.addFooterView(footerView);
//                loadMoreStatus = LOAD_MORE_STATUS_DEFAULT;
//            }
//            if (!isLoadData) {
//                mFlowData.setSId(pagerBean.getSid());
//                mFlowData.setSpId(pagerBean.getSpId());
//                mFlowData.setSpType(pagerBean.getSpType());
//                isLoadData = true;
//                FlowDataEventAnalysisKt.flowDataPageCommoditySearch(mFlowData);
//                recommendAdapter.setFlowData(mFlowData);
//            }
//        });
//        mViewModel.getRecommendSearchData(paramsMap);
//    }
//
//    PayNoticeViewModel payNoticeViewModel = null;
//
//    private void initPayNotice() {
//
//        payNoticeViewModel = new ViewModelProvider(this).get(PayNoticeViewModel.class);
//        payNoticeViewModel.getPayNoticeBeanLiveData().observe(this, applyNoticeBeansOrg -> {
//            // 订单通知
//            List<ApplyNoticeBean> applyNoticeBeans = applyNoticeBeansOrg.data;
//            if (applyNoticeBeans == null || applyNoticeBeans.size() == 0) {
//                llMarqueuePayNotice.setVisibility(View.GONE);
//            } else {
//                llMarqueuePayNotice.setVisibility(View.VISIBLE);
//                List<View> viewList = MarqueeViewKt.createViewList(marqueePayNotice, applyNoticeBeans);
//                marqueePayNotice.startWithViewList(viewList, 1);
//            }
//        });
//        payNoticeViewModel.getPayNotice(SpUtil.getMerchantid());
//
//    }
//
//    //title的高度
//    private int titleHeight;
//    //前置偏移量
//    private int preposition = 120;
//
//    private float level = 3.0f;
//
//    private void setAnimation() {
//        sc.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (nestedScrollView, scrollX, scrollY, oldScrollX, oldScrollY) -> {
//            if (titleHeight == 0) {
//                titleHeight = llTitle.getHeight();
//            }
//
//            float alpha = (scrollY - preposition) * level / titleHeight;
//            if (alpha >= 0) {
//                //llTitle.setAlpha(alpha);
//                tvTitle.setAlpha(alpha);
//                llTitle.setBackground(getResources().getDrawable(R.drawable.base_header_dark_bg));
//            } else {
//                //llTitle.setAlpha(0);
//                tvTitle.setAlpha(0);
//                llTitle.setBackground(null);
//            }
//
//            if (!isKaUser && scrollY > (nestedScrollView.getChildAt(0).getMeasuredHeight() - nestedScrollView.getMeasuredHeight() - ConvertUtils.dp2px(50)) && loadMoreStatus != LOAD_MORE_STATUS_NO_MORE && loadMoreStatus != LOAD_MORE_STATUS_LOADING) {
//                // 底部
//                loadMoreStatus = LOAD_MORE_STATUS_LOADING;
//                if (mParamsMap != null) {
//                    mViewModel.getRecommendSearchData(mParamsMap);
//                }
//            }
//
//        });
//    }
//
//    /**
//     * 获取个人中心展位数据
//     */
//    private void getBoothData() {
//        RequestParams requestParams = new RequestParams();
//        requestParams.put("sceneType", "3"); //CMS场景类型。1首页，3个人中心，4排行榜，6支付结果页
//        HttpManager.getInstance().post(AppNetConfig.EXHIBITIONPOSITION, requestParams, new BaseResponse<BoothData>() {
//            @Override
//            public void onSuccess(String content, BaseBean<BoothData> obj, BoothData boothData) {
//                super.onSuccess(content, obj, boothData);
//                if (boothData == null || obj == null || boothData.detail == null) return;
//                BoothDetail boothDetail = boothData.detail;
//                if (!obj.isSuccess()) return;
//                if (boothDetail.bannerDto == null && boothDetail.steamerDto == null) {
//                    mineBanner.setVisibility(View.GONE);
//                    mineStreamer.setVisibility(View.GONE);
//                    return;
//                }
//                if (boothDetail.type == 1) {
//                    //轮播类型
//                    mineStreamer.setVisibility(View.GONE);
//                    mineBanner.setVisibility(View.VISIBLE);
//                    if (boothDetail.bannerDto == null) return;
//                    mineBanner.setData(boothDetail.bannerDto.imageDtos);
//                } else if (boothDetail.type == 2) {
//                    //胶囊类型
//                    boothDetail.steamerDto.setHotZone(true);
//                    mineBanner.setVisibility(View.GONE);
//                    mineStreamer.setVisibility(View.VISIBLE);
//                    if (boothDetail.steamerDto == null) return;
//                    mineStreamer.setStreamer(boothDetail.steamerDto);
//                }
//
//            }
//
//            @Override
//            public void onFailure(NetError error) {
//                super.onFailure(error);
//            }
//        });
//    }
//
//    private int page = 1;
//    private int limit = 10;
//    private boolean isLoadData;//是否加载过数据（是否上传过埋点数据）
//
//    /**
//     * 获取推荐数据
//     */
//    private void getRecommendData() {
//        RequestParams params = new RequestParams();
//        params.put("offset", page + "");
//        params.put("limit", limit + "");
//        params.put("pageType", "7");//2 发现页为你推荐 3 首页为你推荐 4 商品详情页为你推荐5 购物车页推荐 6 付款结果页推荐 7 我的-个人中心页内推荐
//        params.put("csuId", SpUtil.getMerchantid());
//        params.put("merchantId", SpUtil.getMerchantid());
//        params.put("timestamp", System.currentTimeMillis() + "");
//        if (isLoadData) FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, mFlowData);
//        // guanchong 为你推荐（个人中心底部）
//        HttpManager.getInstance().post(AppNetConfig.HOME_RECOMMENDED_SKU, params, new BaseResponse<RefreshWrapperPagerBean<RowsBean>>() {
//            @Override
//            public void onSuccess(String content, BaseBean<RefreshWrapperPagerBean<RowsBean>> obj, RefreshWrapperPagerBean<RowsBean> data) {
//                super.onSuccess(content, obj, data);
//                if (obj != null && obj.isSuccess() && data != null && getContext() != null) {
//                    page++;
//                    recommendAdapter.removeFooterView(footerView);
//                    recommendList.addAll(data.getRows());
//                    // 请求并更新折后价
//                    AdapterUtils.INSTANCE.getAfterDiscountPrice(data.getRows(), recommendAdapter);
//                    recommendAdapter.notifyDataSetChanged();
//                    recommendAdapter.notifyDataChangedAfterLoadMore(true);
//                    if (limit > data.getRows().size()) {
//                        recommendAdapter.addFooterView(View.inflate(getContext(), R.layout.not_loading, null));
//                        loadMoreStatus = LOAD_MORE_STATUS_NO_MORE;
//                    } else {
//                        if (footerView == null) {
//                            footerView = View.inflate(getContext(), R.layout.view_loadmore, null);
//                        }
//                        recommendAdapter.addFooterView(footerView);
//                        loadMoreStatus = LOAD_MORE_STATUS_DEFAULT;
//                    }
//                    if (!isLoadData) {
//                        mFlowData.setSId(data.getSid());
//                        mFlowData.setSpId(data.getSpId());
//                        mFlowData.setSpType(data.getSpType());
//                        isLoadData = true;
//                        FlowDataEventAnalysisKt.flowDataPageCommoditySearch(mFlowData);
//                        recommendAdapter.setFlowData(mFlowData);
//                    }
//                }
//            }
//
//            @Override
//            public void onFailure(NetError error) {
//                super.onFailure(error);
//            }
//
//            @Override
//            public BaseBean json(String content, Type type) {
//                return super.json(content, new TypeToken<BaseBean<RefreshWrapperPagerBean<RowsBean>>>() {
//                }.getType());
//            }
//        });
//    }
//
//    @Override
//    protected void initTitle() {
//
//        // 为异形屏幕增加状态栏留白
//        ViewGroup.LayoutParams layoutParams = llTitle.getLayoutParams();
//        layoutParams.height += StatusBarUtils.getStatusBarHeight(getNotNullActivity());
//        llTitle.setLayoutParams(layoutParams);
//
//        setMoneyValue(new MerchantInfo.AccountInfo());
//    }
//
//    @Override
//    protected RequestParams getParams() {
//        return new RequestParams();
//    }
//
//    @Override
//    protected String getUrl() {
//        return null;
//    }
//
//    @Override
//    public int getLayoutId() {
//        return R.layout.fragment_more;
//    }
//
//    @OnClick({R.id.ll_kefu, R.id.title_right, R.id.rl_order_form
//            , R.id.detail_wait_ll, R.id.detail_process_ll, R.id.detail_finish_ll
//            , R.id.detail_wait_payment_ll, R.id.detail_refund_ll, R.id.ll_coupon, R.id.title_integral, R.id.ll_balance, R.id.ll_virtual_money
//            , R.id.ll_CustomerService, R.id.title_left
//            , R.id.ll_base_info, R.id.ll_sale_rule
//            , R.id.tv_activity_matter, R.id.tv_account_info, R.id.ll_my_service, R.id.ll_exclusive_market
//            , R.id.ll_personal_help, R.id.iv_vip_gift, R.id.iv_vip_gift_close
//            , R.id.ll_aptitude_download, R.id.ll_my_banking, R.id.ll_feedback, R.id.ll_red_envelope_balance})
//    public void clickTab(View view) {
//        Intent intent;
//        switch (view.getId()) {
//            case R.id.ll_virtual_money:
//                RoutersUtils.open("ybmpage://myvirtualmoney");
//                HashMap<String, String> trackMap = new HashMap<>();
//                trackMap.put("text", "购物金");
//                XyyIoUtil.track("action_virtualGold_Click",trackMap);
//                break;
//
//            case R.id.title_left://消息
//                Message.openMessagePage();
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_MESSAGE);
//
//                break;
//            case R.id.ll_balance://账户余额
////                ((BaseActivity) getNotNullActivity()).gotoAtivity(BalanceActivity.class, null);
////                XyyIoUtil.track(XyyIoUtil.ACTION_ME_SURPLUSMONEY);
//                break;
//
//            case R.id.ll_red_envelope_balance://红包
//                if (redEnvelopGold != null) {
//                    String redEnvelopeUrl = "ybmpage://myredenvelope?totalAmount=" + redEnvelopGold;
//                    RoutersUtils.open(redEnvelopeUrl);
//                    HashMap<String, String> map = new HashMap<>();
//                    map.put("name", "红包");
//                    map.put("action", redEnvelopeUrl);
//                    XyyIoUtil.track("action_Me_RedPacket", map);
//                }
//                break;
//            case R.id.ll_coupon://优惠券
//                ((BaseActivity) getNotNullActivity()).gotoAtivity(CouponMemberActivity.class, null);
////                Bundle bundle = new Bundle();
////                bundle.putString("voucherTemplateId", "6318");
////                ((BaseActivity) getNotNullActivity()).gotoAtivity(CouponToUseActivity.class, bundle);
////                XyyIoUtil.track(XyyIoUtil.ACTION_ME_COUPONS);
//                break;
//
//            case R.id.title_right://设置
//                promptPopup.setVisibility(View.GONE);
//                ((BaseActivity) getNotNullActivity()).gotoAtivity(ElsePageActivity.class, null);
//                break;
//            case R.id.detail_wait_payment_ll://待支付
//                intent = new Intent(getNotNullActivity(), OrderListActivity.class);
//                intent.putExtra(IntentCanst.ORDER_STATE, "10");
//                startActivity(intent);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_NOPAID);
//                break;
//            case R.id.detail_refund_ll://售后退款
//                intent = new Intent(getNotNullActivity(), OrderListActivity.class);
//                intent.putExtra(IntentCanst.ORDER_STATE, "90");
//                startActivity(intent);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_REFUNDAFTERSALE);
//                break;
//            case R.id.rl_order_form://我的订单
//                intent = new Intent(getNotNullActivity(), OrderListActivity.class);
//                intent.putExtra(IntentCanst.ORDER_STATE, "0");
//                startActivity(intent);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_ALLORDERS);
//                break;
//            case R.id.detail_wait_ll://待配送
//                intent = new Intent(getNotNullActivity(), OrderListActivity.class);
//                intent.putExtra(IntentCanst.ORDER_STATE, "1");
//                startActivity(intent);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_NODISTRIBUTION);
//                break;
//            case R.id.detail_process_ll://配送中
//                intent = new Intent(getNotNullActivity(), OrderListActivity.class);
//                intent.putExtra(IntentCanst.ORDER_STATE, "2");
//                startActivity(intent);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_INDISTRIBUTION);
//                break;
//            case R.id.detail_finish_ll: //  待领取status=5  10.6改为待评价status=101表示待评价商品
//                intent = new Intent(getNotNullActivity(), OrderListActivity.class);
//                intent.putExtra(IntentCanst.ORDER_STATE, "101");
//                startActivity(intent);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_EVALUATION);
//                break;
//            case R.id.title_integral://会员签到
//                ((BaseActivity) getNotNullActivity()).gotoAtivity(MemberSignActivity.class, null);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_INTEGRALSIGNIN);
//                break;
//            case R.id.ll_exclusive_market:
//                //专属销售
//                setExclusiveMarket();
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_EXCLUSIVESALES);
//                break;
//            case R.id.ll_sale_rule:   //售后规则
//                RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.RULE);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_AFTERSALESRULES);
//                break;
//            case R.id.ll_CustomerService: // 控价规则
//                //这个静态的地址需要使用https协议
//                String url = AppNetConfig.RULE_PRICE_CONTROL + SpUtil.getMerchantid();
//                if (url.startsWith("http:")) {
//                    url = url.replace("http", "https");
//                }
//                RoutersUtils.open("ybmpage://commonh5activity?url=" + url);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_CONTROLPRICERULES);
//                break;
////            case R.id.ll_clause:  //服务条款
////                setTermsOfService();
////                XyyIoUtil.track(XyyIoUtil.ACTION_ME_SERVICETERMS);
////                break;
//            case R.id.ll_feedback:   //意见反馈
//                String fdUrl = AppNetConfig.FEED_BACK + SpUtil.getMerchantid();
//                if (fdUrl.startsWith("http:")) {
//                    fdUrl = fdUrl.replace("http", "https");
//                }
//                RoutersUtils.open("ybmpage://commonh5activity?url=" + fdUrl);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_FEEDBACK);
//                break;
//            case R.id.ll_my_service:
//                //在线客服
//                sendOnLineService();
//                updateCallServiceStatus();
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_CUSTOMERSERVICE);
//                break;
//            case R.id.ll_personal_help:
//                //常见问题
//                setPersonalHelp();
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_COMMONPROBLEMS);
//                break;
//            case R.id.ll_kefu://客服电话
//                RoutersUtils.telKefu(true);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ME_CUSTOMERSERVICETELEPHONE);
//                break;
//            case R.id.tv_activity_matter:
//                final AlertDialogEx alert = new AlertDialogEx(getNotNullActivity());
//                alert.setTitle("提醒");
//                alert.setMessage("因活动火爆，您的订单可能延迟生成，若还未生成，请耐心等候");
//                alert.setConfirmButton("我知道了", new AlertDialogEx.OnClickListener() {
//                    @Override
//                    public void onClick(AlertDialogEx dialog, int button) {
//                        dialog.dismiss();
//                    }
//                });
//                alert.show();
//                break;
//            case R.id.tv_account_info://账户管理
//                if (!TextUtils.isEmpty(textViewShop.getText().toString())) {
//                    //((BaseActivity) getNotNullActivity()).gotoAtivity(AccountManagerActivity.class, null);
//                    //基本资料和账户管理合到一起了
//                    RoutersUtils.open("ybmpage://accountbasicinfo");
//                } else {
//                    ToastUtils.showShort("网络未连接，请检查网络");
//                }
//                break;
//            case R.id.iv_vip_gift:
//                //会员大礼包
//                if (bean != null && bean.giftSkulist != null && bean.giftSkulist.size() > 0) {
//                    AdDialog3.showDialog(bean);
//                }
//                rlMineGiftPop.setVisibility(View.GONE);
//                break;
//            case R.id.iv_vip_gift_close:
//                getData();
//                rlMineGiftPop.setVisibility(View.GONE);
//                break;
//            case R.id.ll_aptitude_download://小药药资质
//                // 2020-02-10 小药药资质
//                RoutersUtils.open("ybmpage://aptitudexyy");
//                break;
//            case R.id.ll_my_banking://我的金融
//                if (iousIsOpen == IOUS_IS_OPEN) {//湖北域开通
//                    if (isBlack == WHITE_LIST_USER) {//白名单用户
//                        RoutersUtils.open(creditExtensionUrl);
//                    } else {//若不在【白名单】则跳转至【贷款申请心愿单】收集页面，
//                        RoutersUtils.open("ybmpage://mybankingforwishlist?shop_name=" + shopName + "&name=" + name + "&phone=" + phone);
//                    }
//                } else {//非湖北域
//                    showBankingNotOpen();
//                }
//                break;
//            default:
//                break;
//        }
//    }
//
//    /**
//     * 更新在线客服未读消息状态为全部已读
//     */
//    private void updateCallServiceStatus() {
//        RequestParams params = new RequestParams();
//        params.put("merchantId", SpUtil.getMerchantid());
//        params.put("messageType", "2");
//        HttpManager.getInstance().post(AppNetConfig.ONLINE_SERVICE_INFO_STATUS_UPDATE, params, new BaseResponse<String>());
//    }
//
//    private void showBankingNotOpen() {//显示非湖北域未开通弹窗
//        AlertDialogEx dialogEx = new AlertDialogEx(getNotNullActivity());
//        dialogEx.setTitle("温馨提示")
//                .setMessage("金融产品试运行，该区域未开通此服务，敬请期待！")
//                .setConfirmButton("我知道了", (dialog, button) -> {
//
//                })
//                .setMessageGravity(Gravity.CENTER)
//                .setCancelable(false)
//                .setCanceledOnTouchOutside(false)
//                .show();
//    }
//
//    private void getData() {
//        if (bean == null || bean.id <= 0) {
//            return;
//        }
//
//        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.GIFTPACKAGE_RECORDLATESTGIFTBAG)
//                .addParam("merchantId", HttpManager.getInstance().getMerchant_id())
//                .addParam("giftId", bean.id + "")
//                .build();
//        HttpManager.getInstance().post(params, new BaseResponse<EmptyBean>() {
//
//            @Override
//            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean emptyBean) {
//                if (llTitle == null) {
//                    return;
//                }
//                if (obj != null && obj.isSuccess()) {
//                    if (!TextUtils.isEmpty(obj.msg)) {
//                        ToastUtils.showShort(obj.msg);
//                    }
//                }
//            }
//        });
//    }
//
//    // 2019/3/27 大礼包
//    private void getVipGift() {
//
//        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.GIFTPACKAGE_FINDLATESTGIFTBAG).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
//        HttpManager.getInstance().post(params, new BaseResponse<GiftSkulistBean>() {
//
//            @Override
//            public void onSuccess(String content, BaseBean<GiftSkulistBean> obj, GiftSkulistBean bean) {
//                if (llTitle == null) {
//                    return;
//                }
//                if (obj != null && obj.isSuccess()) {
//                    if (bean != null && bean.giftSkulist != null && bean.giftSkulist.size() > 0) {
//                        rlMineGiftPop.setVisibility(View.VISIBLE);
//                        MineFragment.this.bean = bean;
//                    } else {
//                        rlMineGiftPop.setVisibility(View.GONE);
//                    }
//                } else {
//                    rlMineGiftPop.setVisibility(View.GONE);
//
//                }
//            }
//
//            @Override
//            public void onFailure(NetError error) {
//                super.onFailure(error);
//                rlMineGiftPop.setVisibility(View.GONE);
//            }
//        });
//
//    }
//
//    /*
//     * 常见问题
//     * */
//    private void setPersonalHelp() {
//        ((BaseActivity) getNotNullActivity()).gotoAtivity(PersonalHelpActivity.class, null);
//    }
//
//    /*
//     * 服务条款
//     * */
//    private void setTermsOfService() {
//        RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.TERM_SERVICE);
//    }
//
//    private void setExclusiveMarket() {
//        String merchantId = SpUtil.getMerchantid();
//        RequestParams params = new RequestParams();
//        params.put("merchantId", merchantId);
//        HttpManager.getInstance().post(AppNetConfig.GET_SALES_INFO, params, new BaseResponse<SalesInfoBean>() {
//
//            @Override
//            public void onSuccess(String content, BaseBean<SalesInfoBean> obj, SalesInfoBean bean) {
//
//                if (obj != null && obj.isSuccess()) {
//
//                    if (bean != null) {
//                        if (!TextUtils.isEmpty(bean.phone)) {
//                            setAlertDialogEx(bean.phone, new AlertDialogEx.OnClickListener() {
//                                @Override
//                                public void onClick(AlertDialogEx dialog, int button) {
//                                    Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + bean.phone));
//                                    BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
//                                }
//                            });
//                        }
//                    }
//                }
//            }
//        });
//    }
//
//    public void setAlertDialogEx(String phone, AlertDialogEx.OnClickListener onClickListener) {
//        AlertDialogEx dialogEx = new AlertDialogEx(BaseYBMApp.getApp().getCurrActivity());
//        dialogEx.setMessage("呼叫专属销售：" + phone).setCancelButton("取消", null).setConfirmButton("呼叫", onClickListener).show();
//    }
//
//    /*
//     * 在线客服
//     * */
//    private void sendOnLineService() {
//
//        RequestParams params = new RequestParams();
//
//        HttpManager.getInstance().post(AppNetConfig.GET_IM_PACKURL, params, new BaseResponse<ImPackUrlBean>() {
//
//            @Override
//            public void onSuccess(String content, BaseBean<ImPackUrlBean> obj, ImPackUrlBean baseBean) {
//
//                if (obj != null && obj.isSuccess()) {
//
//                    if (baseBean != null) {
//                        RoutersUtils.open("ybmpage://commonh5activity?cache=0&url=" + getGotoImUrl(baseBean.IM_PACK_URL));
//                    }
//                }
//
//            }
//
//        });
//
//    }
//
//    public String getGotoImUrl(String url) {
//        String scUrl = url;
//        String merchantId = SpUtil.getMerchantid();
//        scUrl = scUrl + "&userid=" + merchantId + "&sc=1003" + "&portalType=1";
//        return scUrl;
//    }
//
//    @Override
//    public void onDestroyView() {
//        super.onDestroyView();
//        ButterKnife.unbind(this);
//        if (mInfoAndMsgReceiver != null) {
//            LocalBroadcastManager.getInstance(getNotNullActivity().getApplicationContext()).unregisterReceiver(mInfoAndMsgReceiver);
//        }
//        Message.instance.releaseListener(listener);
//    }
//
//
//    /**
//     * 我的 - 常用工具
//     */
//    private void getUseTools() {
//        RequestParams params = new RequestParams();
//        params.put("codemap", "USE_TOOLS");
//        params.put("merchantId", SpUtil.getMerchantid());
//        HttpManager.getInstance().post(AppNetConfig.USETOOLS, params, new BaseResponse<List<UseToolsBean>>() {
//            @Override
//            public void onSuccess(String content, BaseBean<List<UseToolsBean>> obj, List<UseToolsBean> useToolsBeans) {
//                if (obj != null && useToolsBeans != null) {
//                    commonToolsData.clear();
//                    // 常够清单 本地写死
//                    commonToolsData.add(new UseToolsBean("19", "常购清单", "", "ybmpage://oftenBuy", "action_Me_OftenBuy", null));
//
//                    commonToolsData.addAll(useToolsBeans);
//                    commonToolsAdapter.notifyDataSetChanged();
//                }
//            }
//
//            @Override
//            public void onFailure(NetError error) {
//                super.onFailure(error);
//            }
//        });
//    }
//
//    /*
//     * 我的-商户基本信息
//     * */
//    public void getMerchantBaseInfo(boolean show) {
//        if (show) {
//            showProgress();
//        }
//        final String merchantId = SpUtil.getMerchantid();
//        RequestParams params = new RequestParams();
//        params.put("merchantId", merchantId);
//        HttpManager.getInstance().post(AppNetConfig.BASE_INFO, params, new BaseResponse<MerchantInfo>() {
//
//            @Override
//            public void onSuccess(String content, BaseBean<MerchantInfo> obj, MerchantInfo data) {
//                if (show) {
//                    dismissProgress();
//                }
//                if (textViewShop == null) {
//                    return;
//                }
//                if (obj.isSuccess() && data != null) {
//                    // 是否是ka用户
//                    SpUtil.writeBoolean(SpConstantKt.SP_KEY_LOGIN_IS_KA, data.isKa);
//
//                    SpUtil.setValidityStatus(data.validity);
//
//                    if (!TextUtils.isEmpty(data.notice)) {
//                        tvActivityMatter.setText(data.notice);
//                    }
//                    MerchantInfo.AccountInfo account = data.accountInfo;
//                    if (account != null) {
//                        setMoneyValue(account);
//                    }
//
//                    // 2020-02-10 小药药资质
//                    if (data.licenseDownStatus == 1) {//1有权限查看 0无权限
//                        llAptitudeDownload.setVisibility(View.VISIBLE);
//                    } else {
//                        llAptitudeDownload.setVisibility(View.INVISIBLE);
//                    }
//
//                    MerchantInfo.BaseInfo baseInfo = data.baseInfo;
//                    if (baseInfo != null) {
//                        //小药白条心愿单传递参数赋值
//                        shopName = baseInfo.realName;
//                        name = baseInfo.nickname;
//                        phone = baseInfo.mobile;
//
//                        textViewShop.setText(baseInfo.realName);
//                        String name = "姓名:" + baseInfo.nickname;
//                        String mobile = "电话:" + baseInfo.mobile;
//                        String defaultAddress = "地址:" + (TextUtils.isEmpty(baseInfo.address) ? "请添加默认地址" : baseInfo.address);
//
//                        //请求完数据更新本地的信息
//                        AccountTable.update(getNotNullActivity(), merchantId, baseInfo.realName, baseInfo.mobile, baseInfo.address);
//
//                        //region  诸葛重新绑定账户
//                        SpUtil.writeString(ConstantData.PROVINCECODE, baseInfo.provinceCode);
//                        SpUtil.writeString(ConstantData.PROVINCE, baseInfo.province);
//                        XyyIoUtil.identify(merchantId, baseInfo);
//                        //endregion
//                    }
//                    if (data.tagList != null && !data.tagList.isEmpty()) {
//                        //更新推送tags
//                        PushUtil.setTags(data.tagList);
//                    }
//                    //设置我的金融
//                    initMyBanking(data);
//                }
//            }
//
//            @Override
//            public void onFailure(NetError error) {
//                dismissProgress();
//            }
//        });
//    }
//
//    /**
//     * 获取在线客服消息数量
//     */
//    public void getOnlineServiceInfoCount() {
//        RequestParams requestParams = new RequestParams();
//        requestParams.put("messageType", "2");
//        requestParams.put("merchantId", SpUtil.getMerchantid());
//        HttpManager.getInstance().post(AppNetConfig.ONLINE_SERVICE_INFO_COUNT, requestParams, new BaseResponse<OnlinServiceInfoBean>() {
//            @SuppressLint("SetTextI18n")
//            @Override
//            public void onSuccess(String content, BaseBean<OnlinServiceInfoBean> obj, OnlinServiceInfoBean onlinServiceInfoBean) {
//                super.onSuccess(content, obj, onlinServiceInfoBean);
//                if (onlinServiceInfoBean != null && onlinServiceInfoBean.getMessageType() == 2) {
//                    if (onlinServiceInfoBean.getCount() > 0 && onlinServiceInfoBean.getCount() < 100) {
//                        //显示数量
//                        tvMyServiceNum.setVisibility(View.VISIBLE);
//                        tvMyServiceNum.setText(onlinServiceInfoBean.getCount() + "");
//                    } else if (onlinServiceInfoBean.getCount() > 99) {
//                        //显示99+
//                        tvMyServiceNum.setVisibility(View.VISIBLE);
//                        tvMyServiceNum.setText("99+");
//                    } else {
//                        //隐藏
//                        tvMyServiceNum.setVisibility(View.GONE);
//                    }
//                }
//            }
//        });
//    }
//
//    private void initMyBanking(MerchantInfo data) {
//        iousIsOpen = data.iousIsOpen;
//        creditExtensionUrl = data.creditExtensionUrl;
//        String totalQuota = data.totalQuota;
//        isBlack = data.isBlack;
//
//        if (!TextUtils.isEmpty(totalQuota) && totalQuota.contains("万")) {
//            String[] totalQuotaArr = totalQuota.split("万");
//            tvMyBanking.setText(Html.fromHtml(String.format(getResources().getString(R.string.str_mine_my_banking_number), totalQuotaArr[0], "万")));
//        } else {
//            tvMyBanking.setText(totalQuota);
//        }
//
//    }
//
//    /**
//     * 获取订单的状态
//     * url => "orders/findNumGroupByStatus"
//     */
//    private void getOderStatus() {
//
//        refreshUnpayStatus();
//
//        String merchantId = SpUtil.getMerchantid();
//        RequestParams params = new RequestParams();
//        params.put("merchantId", merchantId);
//        HttpManager.getInstance().post(AppNetConfig.ORDER_STATUS_NUMER, params, new BaseResponse<OrderStatusNumber>() {
//            @Override
//            public void onSuccess(String content, BaseBean<OrderStatusNumber> obj, OrderStatusNumber data) {
//                if (obj != null && obj.isSuccess() && data != null) {
//                    if (tvSmgWaitPay == null) {
//                        return;
//                    }
//                    tvSmgWaitPay.setVisibility(data.waitPayNum > 0 ? View.VISIBLE : View.GONE);
//                    tvSmgWaitPay.setText(String.valueOf(data.waitPayNum));
//                    tvSmgWaitDeliver.setVisibility(data.waitShippingNum > 0 ? View.VISIBLE : View.GONE);
//                    tvSmgWaitDeliver.setText(String.valueOf(data.waitShippingNum));
//                    tvSmgWaitReceive.setVisibility(data.waitReceiveNum > 0 ? View.VISIBLE : View.GONE);
//                    tvSmgWaitReceive.setText(String.valueOf(data.waitReceiveNum));
//                    tvSmgWaitBalance.setVisibility(data.waitAppraiseNum > 0 ? View.VISIBLE : View.GONE);
//                    tvSmgWaitBalance.setText(String.valueOf(data.waitAppraiseNum));
//                    tvBalance.setVisibility(data.waitDrawBlanceNum > 0 ? View.VISIBLE : View.GONE);
//                    tvSmgWaitService.setVisibility(data.refundNum > 0 ? View.VISIBLE : View.GONE);
//                    tvSmgWaitService.setText(String.valueOf(data.refundNum));
//
//                }
//            }
//        });
//    }
//
//    private void setMoneyValue(MerchantInfo.AccountInfo baseInfo) {
//        String integral = baseInfo.signPointCount == 0 ? "0" : baseInfo.signPointCount + "";
//        String balance = TextUtils.isEmpty(baseInfo.balance) ? "0" : baseInfo.balance;
//        redEnvelopGold = TextUtils.isEmpty(baseInfo.redPacketBalance) ? "0" : UiUtils.transform(baseInfo.redPacketBalance);
//        String coupon = baseInfo.voucherCount == 0 ? "0" : baseInfo.voucherCount + "";
//        if (balance_tv == null || coupon_tv == null) {
//            return;
//        }
//        // integral_tv.setText(integral);
//        balance_tv.setText(balance);
//        coupon_tv.setText(coupon);
//        tvRedEnvelope.setText(redEnvelopGold);
//        if (baseInfo.virtualGoldShowFlag == 1) {
//            llVirtualMoney.setVisibility(View.VISIBLE);
//            tvVirtualMoney.setText(TextUtils.isEmpty(baseInfo.virtualGold) ? "0.00" : UiUtils.transform(baseInfo.virtualGold));
//        } else {
//            llVirtualMoney.setVisibility(View.GONE);
//        }
//    }
//
//    private void initReceiver() {
//        IntentFilter intentFilter = new IntentFilter();
//        intentFilter.addAction(IntentCanst.ACTION_MERCHANTBASEINFO);
//        intentFilter.addAction(IntentCanst.ACTION_ORDER_STATUS);
//        intentFilter.addAction(IntentCanst.ACTION_SWITCH_USER);
//        LocalBroadcastManager.getInstance(getNotNullActivity().getApplicationContext()).registerReceiver(mInfoAndMsgReceiver, intentFilter);
//
//    }
//
//    @Override
//    public void onHiddenChanged(boolean hidden) {
//        super.onHiddenChanged(hidden);
//        if (!hidden) {
//            String kefuPhone = "客服电话：" + RoutersUtils.kefuPhone;
//            llKefuTv1.setText(kefuPhone);
//            refreshUserInfo();
//        }
//    }
//
//
//    private Message.Listener listener = new Message.Listener() {
//        @Override
//        public void countChange(int count) {
//            Message.showMsgCount(count, tvSmgNum, tvSmgNumMore);
//            // Message.showMsgCount(count, tvSmgNum1, tvSmgNumMore1);
//        }
//    };
//
//    /**
//     * 查看认证信息
//     */
//    private void getInfoByMerchantId() {
//        RequestParams params = new RequestParams();
//        params.put("merchantId", SpUtil.getMerchantid());
//        HttpManager.getInstance().post(AppNetConfig.GET_INFOBY_MERCHANTID, params, new BaseResponse<InfoByMerchantIdBean>() {
//
//            @Override
//            public void onSuccess(String content, BaseBean<InfoByMerchantIdBean> obj, InfoByMerchantIdBean infoByMerchantIdBean) {
//                super.onSuccess(content, obj, infoByMerchantIdBean);
//                if (infoByMerchantIdBean != null) {
//                    //0-未认证，1-审核中，2-审核失败，3-审核成功，4-失效
//                    int status = infoByMerchantIdBean.status;
//                    //AuthSwitch这个字段  1是开  0是关
//                    int authSwitch = infoByMerchantIdBean.authSwitch;
//                    if (authSwitch == 1) {
//                        if (status == 2 || status == 0 || status == 4) {
//                            SpUtil.writeBoolean("isVerificationAlert", false);
//                            promptPopup.setVisibility(View.VISIBLE);
//                            if (status == 2) {//失败
//                                TextColorChangeUtils.setInterTextColor(getContext(), promptPopup.findViewById(R.id.tv_des), "被委托人信息", "认证失败", "", R.color.detail_tv_color_ff2121);
//                            } else if (status == 0) {//未认证
//                                TextColorChangeUtils.setInterTextColor(getContext(), promptPopup.findViewById(R.id.tv_des), "新增", "“被委托人信息认证”", "功能", R.color.color_1E1E24);
//                            } else if (status == 4) {//失效
//                                TextColorChangeUtils.setInterTextColor(getContext(), promptPopup.findViewById(R.id.tv_des), "被委托人信息", "已失效", "", R.color.detail_tv_color_ff2121);
//                            }
//                        } else {
//                            promptPopup.setVisibility(View.GONE);
//                        }
//                    } else {
//                        promptPopup.setVisibility(View.GONE);
//                    }
//
//                }
//            }
//
//            @Override
//            public void onFailure(NetError error) {
//                super.onFailure(error);
//                promptPopup.setVisibility(View.GONE);
//            }
//        });
//
//
//    }
//}
