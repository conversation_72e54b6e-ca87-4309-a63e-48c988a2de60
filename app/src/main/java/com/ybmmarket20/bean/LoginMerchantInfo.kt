package com.ybmmarket20.bean

/**
 * 店铺信息
 */
data class LoginMerchantInfo(
    var licenseStatus: Int,
    var fullAddress: String?,
    var phone: String?,
    var remarkAddress: String?,
    var shopName: String?,
    var realName: String?,
    var province: String?,
    var provinceCode: String?,
    var city: String?,
    var cityCode: String?,
    var district: String?,
    var areaCode: String?,
    var activeTime: String?,
    var streetCode: String?,
    var street: String?,
    var validity: Int,
    //是否是爬虫
    var isCrawler: Boolean
)

data class LoginMerchantInfoWithPage(
    var list: MutableList<LoginMerchantInfo>?,
    var pages: Int
) {
    fun isEnd(pageNo: Int) = pageNo == pages
}
