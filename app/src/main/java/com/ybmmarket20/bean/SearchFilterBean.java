package com.ybmmarket20.bean;

import androidx.annotation.IntDef;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;
import java.util.Map;

/**
 * 搜索返回类
 */

public class SearchFilterBean implements Serializable {
    public String realName;
    public String id;
    public String nickname;
    public String tag;
    public int position;
    public boolean isTopInManufacturerName;
    public boolean isTopInWorking;

    public boolean isAvailable;//仅看有货
    public boolean isPromotion;//有促销
    public boolean isCanUseCoupon;//可用券

    public boolean isClassA;//甲类otc
    public boolean isClassB;//乙类otc
    public boolean isClassRx;//处方药rx
    public boolean isClassElse;//其他
    public boolean isDpby;//单品包邮

    public boolean isSpellGroupAndPgby;//拼团包邮

    public String nearEffective; //有效期
    public String nearEffectiveStr; //有效期

    public Map<String, String> dynamicLabelResultMap;

    public boolean isShunfeng;//顺丰快递
    public boolean isJD; //京东快递

    public String mustTagList = "";//商品标签列表多个标签用逗号分隔，支持 AND 关系，如：tag1 AND tag2
                                    //店铺服务：顺丰快递-YBM_ST_SERV_LOG_SF，京东快递-YBM_ST_SERV_LOG_JD

    public String priceRangeFloor;//最低价
    public String priceRangeTop;//最高价

    public List<String> lastNames;
    @SerializedName("isShopCart")
    public boolean isSelected = false;

    //region 综合选项
    public static final int SYNTHESIZE = 0;     // 综合
    public static final int SALESVOLUME = 1;    // 销量
    public static final int PRICE = 2;          // 价格

    @IntDef({SYNTHESIZE, SALESVOLUME, PRICE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface SearchOption {
    }


    @SearchOption
    public int selectedSearchOption = SYNTHESIZE;


    /**
     * 获取综合选项栏回显文案
     *
     * @return
     */
    public String getShowSynthesizeStr() {
        String showStr = "";
        switch (selectedSearchOption) {
            case SYNTHESIZE:
                showStr = "排序";
                break;
            case SALESVOLUME:
                showStr = "销量";
                break;
            case PRICE:
                showStr = "价格";
                break;
        }
        return showStr;

    }

    /**
     * 获取综合下拉选项栏文案
     *
     * @return
     */
    public String getPopSynthesizeStr() {
        String popStr = "";
        switch (selectedSearchOption) {
            case SYNTHESIZE:
                popStr = "默认";
                break;
            case SALESVOLUME:
                popStr = "按销量由高到低";
                break;
            case PRICE:
                popStr = "按价格由低到高";
                break;
        }
        return popStr;

    }
    //endregion

    //region 商家相关字段
    public String key;              // 店铺code
    public String showName;         // 店铺名
    public String packagePDTips;    // 包邮差价提示
    public String packageTips;      // 包邮提示
    public int isThreadCompany;     // 是否第三方公司 0-否，1-是
    public int accountStatus;       // 账户状态：0-未开户，1-已开户
    public int quality;       // 品质店铺标签：0-无，1-品质店铺
    @SerializedName("docCount")
    public String specCount; //相同规格商品数量
    //已选中的规格
    public String specSelectedStr = "";
    //已选中商家Code
    public String shopSelectedStr = "";
    //已选中商家名字
    public String shopNameSelectedStr = "";

    public List<SearchDynamicLabelConfig> dynamicLabelConfig;
    //endregion

    public SearchFilterBean() {
    }

    public SearchFilterBean(List<String> lastNames) {
        this.lastNames = lastNames;
    }

    public SearchFilterBean(boolean isAvailable, boolean isPromotion, boolean isClassA, boolean isClassB
            , boolean isClassRx, boolean isClassElse, String priceRangeFloor
            , String priceRangeTop, List<String> lastNames, boolean isShunfeng, boolean isJD, boolean isCanUseCoupon, boolean isDpby, boolean isSpellGroupAndPgby, String nearEffective, String nearEffectiveStr, Map<String, String>  dynamicLabelResultMap) {

        this.isAvailable = isAvailable;
        this.isPromotion = isPromotion;
        this.isCanUseCoupon = isCanUseCoupon;
        this.isClassA = isClassA;
        this.isClassB = isClassB;
        this.isClassRx = isClassRx;
        this.isClassElse = isClassElse;
        this.priceRangeFloor = priceRangeFloor;
        this.priceRangeTop = priceRangeTop;
        this.lastNames = lastNames;
        this.isJD = isJD;
        this.isShunfeng = isShunfeng;
        this.isDpby = isDpby;
        this.isSpellGroupAndPgby = isSpellGroupAndPgby;
        this.nearEffective = nearEffective;
        this.nearEffectiveStr = nearEffectiveStr;
        this.dynamicLabelResultMap = dynamicLabelResultMap;
    }

    public SearchFilterBean(String realName, String id, boolean isTopInManufacturerName, boolean isTopInWorking) {
        this.realName = realName;
        this.id = id;
        this.isTopInManufacturerName = isTopInManufacturerName;
        this.isTopInWorking = isTopInWorking;
    }

    public SearchFilterBean(String realName, String id, int position) {
        this.realName = realName;
        this.id = id;
        this.position = position;
    }

    public SearchFilterBean(String realName, String id) {
        this.realName = realName;
        this.id = id;
    }

    public SearchFilterBean(String realName) {
        this.realName = realName;
    }

    public SearchFilterBean(String realName, String id, String nickName) {
        this.realName = realName;
        this.id = id;
        this.nickname = nickName;
    }

    public SearchFilterBean(String realName, String id, String nickName, String tag) {
        this.realName = realName;
        this.id = id;
        this.nickname = nickName;
        this.tag = tag;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SearchFilterBean bean = (SearchFilterBean) o;

        return id != null ? id.equals(bean.id) : bean.id == null;

    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
