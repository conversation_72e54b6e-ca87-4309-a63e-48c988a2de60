package com.ybmmarket20.view.payType

import android.graphics.Color
import android.text.Html
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.PayWayBean
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 提单页支付类型
 */
class PayTypeAdapterV2(payWayList: MutableList<PayWayBean>): YBMBaseAdapter<PayWayBean>(R.layout.item_pay_type, payWayList) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: PayWayBean?) {
        whenAllNotNull(baseViewHolder, t) {holder, bean ->
            val tvPayName = holder.getView<TextView>(R.id.tvPayName)
            val tvTip = holder.getView<TextView>(R.id.tvTip)
            val ivPaySelected = holder.getView<ImageView>(R.id.ivPaySelected)
            val ivOverDue = holder.getView<ImageView>(R.id.ivOverDue)
            val itemView = holder.itemView
            tvPayName.text = bean.payway
            tvPayName.setTextColor(Color.parseColor(if (bean.checked) "#00B377" else "#292933"))
            tvTip.text = Html.fromHtml(if (bean.tips != null) bean.tips else "")
            tvTip.visibility = if (bean.tips.isNullOrEmpty()) View.GONE else View.VISIBLE
            ivOverDue.visibility = if (bean.isOverdue) View.VISIBLE else View.GONE
            ivPaySelected.visibility = if (bean.checked) View.VISIBLE else View.GONE
            itemView.isActivated = bean.checked

        }
    }
}