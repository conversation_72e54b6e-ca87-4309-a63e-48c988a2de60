package com.ybmmarket20.view.couponrelatedgoods

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.CouponRelatedGoodsPriceBean
import com.ybmmarket20.view.homesteady.whenAllNotNull

class CouponRelatedGoodsPriceFilterView(context: Context, attr: AttributeSet?) :
    RecyclerView(context, attr) {

    private var mItemClickListener: ((couponRelatedGoodsPriceBean: CouponRelatedGoodsPriceBean) -> Unit)? = null

    /**
     * 设置价格列表数据
     */
    fun setPriceList(items: List<CouponRelatedGoodsPriceBean>?) {
        layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        adapter = items?.let { PriceFilterAdapter(it) }
    }

    /**
     * 设置监听
     */
    fun setOnItemClickListener(itemClickListener: ((couponRelatedGoodsPriceBean: CouponRelatedGoodsPriceBean) -> Unit)?) {
        mItemClickListener = itemClickListener
    }

    inner class PriceFilterAdapter(val items: List<CouponRelatedGoodsPriceBean>) :
        YBMBaseAdapter<CouponRelatedGoodsPriceBean>(R.layout.item_coupon_related_goods_price, items) {

        @SuppressLint("NotifyDataSetChanged")
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: CouponRelatedGoodsPriceBean?) {
            whenAllNotNull(baseViewHolder, t) {holder, bean->
                val tvPrice = holder.getView<TextView>(R.id.tvPrice)
                tvPrice.text = bean.text
                tvPrice.isActivated = bean.isSelected
                tvPrice.setTextColor(if (bean.isSelected) Color.parseColor("#00B955") else Color.parseColor("#555555"))
                tvPrice.setOnClickListener {
                    if (tvPrice.isActivated) return@setOnClickListener
                    resetItemList()
                    bean.isSelected = true
                    notifyDataSetChanged()
                    mItemClickListener?.invoke(bean)
                }
            }
        }

        private fun resetItemList() {
            items.forEach {
                it.isSelected = false
            }
        }

    }
}