package com.ybmmarket20.view.homesteady

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.LinearLayout
import android.widget.TextView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.StreamerItem
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.utils.RoutersUtils
import kotlinx.android.synthetic.main.layout_home_steady_streamer.view.*
import java.lang.Exception


/**
 * <AUTHOR>
 * @date 2020-05-11
 * @description 胶囊位
 */
class HomeSteadyStreamerView(context: Context, attr: AttributeSet) : ConstraintLayout(context, attr), IHomeSteady {

    private var analysisCallback: ((String, String) -> Unit)? = null

    override fun initPlaceHold() {
        iv_home_steady_streamer.setImageResource(R.drawable.icon_home_steady_capsule_placehold)
    }

    init {
        View.inflate(context, R.layout.layout_home_steady_streamer, this)
    }

    fun handleImg(width: Int, height: Int, url: String?) {
        if (width == 0 || height == 0) return
        val lp = iv_home_steady_streamer.layoutParams as LayoutParams
        lp.height = (iv_home_steady_streamer.measuredWidth * (height * 1.0f / width)).toInt()
        iv_home_steady_streamer.layoutParams = lp
        // 兼容gif
        if (url != null) {
            ImageHelper.with(context).load(url).placeholder(R.drawable.icon_home_steady_capsule_placehold)
                    .error(R.drawable.icon_home_steady_capsule_placehold).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                    .into(iv_home_steady_streamer)
        }
        val lp1 = ll_hot_area.layoutParams
        lp1.height = lp.height
        ll_hot_area.layoutParams = lp1
    }


    /**
     * 设置图片
     */
    var ivWidth = 0
    fun setStreamer(streamerItem: StreamerItem?) {
        var isFresh = true //防止更新数据但尺寸未变导致图片无法更新
        iv_home_steady_streamer.viewTreeObserver.addOnPreDrawListener {
            if (ivWidth != iv_home_steady_streamer.measuredWidth || isFresh) {
                isFresh = false
                ivWidth = iv_home_steady_streamer.measuredWidth
                ImageHelper.with(context).load(streamerItem?.image).asBitmap().placeholder(R.drawable.icon_home_steady_capsule_placehold)
                    .error(R.drawable.icon_home_steady_capsule_placehold).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                    .into(object : SimpleTarget<Bitmap>() {
                        override fun onResourceReady(resource: Bitmap?, glideAnimation: GlideAnimation<in Bitmap>?) {
                            //获取图片尺寸
                            val width = resource?.width?: 0
                            val height = resource?.height?: 0
                            handleImg(width, height, streamerItem?.image)
                        }

                        override fun onLoadFailed(e: Exception?, errorDrawable: Drawable?) {
                            super.onLoadFailed(e, errorDrawable)
                            handleImg(width, height, null)
                        }
                    })
            }
            true
        }
       if (streamerItem?.isHotZone==true){//有热区
           iv_home_steady_streamer.setOnClickListener(null)
           ll_hot_area.removeAllViews()
           ll_hot_area.visibility= View.VISIBLE
           val hotZoneList= streamerItem.hotZoneInfoList
           //val arr = intArrayOf(R.color.record_red, R.color.city_green_light, R.color.blue)//区别热区
           if (hotZoneList!=null&& hotZoneList.isNotEmpty()){
               var widthTotal=0
               for (i in hotZoneList.indices){
                   val width= hotZoneList[i].hotZoneWidth?:0
                   widthTotal+=width
                   if (width>0){//宽度大于0才往布局里添加覆盖的控件
                       val action=hotZoneList[i].action
                       val tvHotZone =TextView(context)
                       tvHotZone.background= ContextCompat.getDrawable(context, R.color.transparent)
                       tvHotZone.setOnClickListener {
                           RoutersUtils.open(action)
                           clickEvent(action, hotZoneList[i].hotZoneName)
                       }
                       val layoutParams =  LinearLayout.LayoutParams(0, ViewGroup.LayoutParams.MATCH_PARENT, width.toFloat())
                       ll_hot_area.addView(tvHotZone,layoutParams)
                   }
               }
               val blankWidth=100-widthTotal
               if (blankWidth>0){//宽度大于0才往布局里添加覆盖的控件
                   val tvBlank=TextView(context)
                   tvBlank.background = ContextCompat.getDrawable(context,R.color.transparent)
                   val blankLayoutParams = LinearLayout.LayoutParams(0, ConvertUtils.dp2px(90f), blankWidth.toFloat())
                   ll_hot_area.addView(tvBlank, blankLayoutParams)
               }
           }
       }else{
           ll_hot_area.visibility= View.GONE
           iv_home_steady_streamer.setOnClickListener{
               RoutersUtils.open(streamerItem?.action)
               clickEvent(streamerItem?.action, "")
           }
       }
//        var listenerHeight = 0
//        ll_hot_area.viewTreeObserver.addOnPreDrawListener {
//            val lp = ll_hot_area.layoutParams
//            if (listenerHeight != height) {
//                listenerHeight = height
//                lp.height = height
//                ll_hot_area.layoutParams = lp
//            }
//            true
//        }
    }

    /**
     * 点击事件埋点
     */
    private fun clickEvent(action:String?, text: String?){
//        val obj = JSONObject()
//        try {
//            obj.also {
//                it.put("action", action)
//            }
//            XyyIoUtil.track("action_Home_Image", obj)
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
        analysisCallback?.invoke(action?: "", text?: "")
    }
    /**
     * 过渡背景开关
     */
    fun displayGradient(isDisplay: Boolean) {
        v_streamer_gradient.visibility = if (isDisplay) View.VISIBLE else View.GONE
    }

    /**
     * 设置埋点回调
     */
    fun setAnalysisCallback(callback: (action: String, text: String) -> Unit) {
        analysisCallback = callback
    }
}