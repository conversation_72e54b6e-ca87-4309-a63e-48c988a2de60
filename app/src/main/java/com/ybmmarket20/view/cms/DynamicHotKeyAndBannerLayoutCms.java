package com.ybmmarket20.view.cms;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.os.Handler;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.HomeHotSearchAdapter;
import com.ybmmarket20.adapter.RecyclingPagerAdapter;
import com.ybmmarket20.bean.cms.ModuleBeanCms;
import com.ybmmarket20.bean.cms.ModuleContent;
import com.ybmmarket20.bean.cms.ModuleItemBannerBean;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.ImageUtil;
import com.ybmmarket20.view.ClipViewPager;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Brin
 * @date : 2019/7/23 - 15:57
 * @Description :
 */
public class DynamicHotKeyAndBannerLayoutCms extends BaseDynamicLayoutCms<ModuleItemBannerBean> {

    private RelativeLayout rlLayout;
    private ImageView ivBannerBg;
    private ImageView ivBannerRestBg;
    private LinearLayout llHotSearch;
    private TextView tvHotTittle;
    private RecyclerView rvHot;

    private HomeHotSearchAdapter hotSearchAdapter;
    private List<ModuleContent.HotSearch> mHotDataList;

    private ClipViewPager vp_arl;
    private List<ModuleItemBannerBean> items;
    private boolean autoRoll;
    private MyPagerAdapter adapter;
    static Handler handler = new Handler();
    private int stepTime = 3000;
    boolean isRight = true;
    private RelativeLayout mRlLayout;
    protected int style;
    protected int layoutStyle;
    private LinearLayout ll_arl;
    protected boolean showDog;

    private String defDotColor;
    private String activeDotColor;
    // 轮播点透明度 ios不好实现，暂时屏蔽
    private int defAlpha;
    private int activeAlpha;

    private boolean canScroll = false;
    private String currentBgRes = "#00B377";
    private String defaultTopColor = "#00B377";

    public DynamicHotKeyAndBannerLayoutCms(Context context) {
        super(context);
    }

    public DynamicHotKeyAndBannerLayoutCms(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicHotKeyAndBannerLayoutCms(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        rlLayout = findViewById(R.id.rl_layout);
        ivBannerBg = findViewById(R.id.iv_banner_bg);
        ivBannerRestBg = findViewById(R.id.iv_banner_rest_bg);
        llHotSearch = findViewById(R.id.ll_hot_search);
        tvHotTittle = findViewById(R.id.tv_hot_title);
        rvHot = findViewById(R.id.rv_hot);

        tvHotTittle.setTextColor(Color.parseColor("#ffffff"));

        mHotDataList = new ArrayList<>();
        rvHot.setLayoutManager(new WrapLinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        hotSearchAdapter = new HomeHotSearchAdapter(R.layout.item_home_hot, mHotDataList);
        rvHot.setAdapter(hotSearchAdapter);

        // banner 相关初始化
        mRlLayout = (RelativeLayout) findViewById(R.id.rl_layout);
        vp_arl = (ClipViewPager) findViewById(R.id.vp_arl);
        ll_arl = (LinearLayout) findViewById(R.id.ll_arl);
        vp_arl.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_MOVE:
                    case MotionEvent.ACTION_DOWN:
                        setAutoRoll(false);
                        break;
                    case MotionEvent.ACTION_UP:
                        setAutoRoll(true);
                        break;
                }
                return false;
            }
        });


    }

    @Override
    public void setItemData(ModuleBeanCms moduleBean, List<ModuleItemBannerBean> items, boolean isUpdate) {

        if (!isUpdate) {
            return;
        }

        if (moduleBean.content.listHostSearch != null && moduleBean.content.listHostSearch.size() > 0) {
            setHotkeyData(moduleBean.content);
        }

        // 数据为Null　隐藏　banner
        if (items == null || items.size() == 0) {
            mRlLayout.setVisibility(GONE);
            return;
        } else {
            mRlLayout.setVisibility(VISIBLE);
        }
        setStyle(0);
        this.items = items;
        // 处理viewpager
        adapter = new MyPagerAdapter();
        vp_arl.setAdapter(adapter);
        // 移除上次遗留的所有点
        if (showDog) {
            ll_arl.removeAllViews();
            //重新添加点
            addDots();
            vp_arl.addOnPageChangeListener(PageListener);
        }
        vp_arl.setOffscreenPageLimit(Math.min(2, items.size()));


        mRlLayout.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return vp_arl.dispatchTouchEvent(event);
            }
        });

        if (items.size() > 1) {
            vp_arl.setCurrentItem(items.size() * 120, false);
        }
        //自动滚动
        if (stepTime == 0) {
            setAutoRoll(false);
        } else {
            setAutoRoll(true);
        }
    }


    private void setMiddleUI(ModuleContent content, List<ModuleItemBannerBean> items) {
        if (isImageBgOutside(content)) {
            setNetBackground(llHotSearch, content.hotWord_bgRes);
            setNetBackground(ivBannerBg, content.meddle_bgRes);
            setNetBackground(ivBannerRestBg, content.bottom_bgRes);
        } else if (items != null && items.size() > 0) {
            String bgRes = items.get(0).bgRes;
            String rest_bgRes = items.get(0).rest_bgRes;
            setNetBackground(llHotSearch, bgRes);
            setNetBackground(ivBannerBg, bgRes);
            setNetBackground(ivBannerRestBg, rest_bgRes);
        } else {
            setNetBackground(llHotSearch, defaultTopColor);
        }

        if (!isImageBgOutside(content) && items != null && items.size() > 0) {
            currentBgRes = items.get(0).bgRes;
        }
    }

    /**
     * 设置banner的指示点颜色，轮播间隔时间
     *
     * @param content
     */
    private void setBannerUi(ModuleContent content) {
        defDotColor = content.style.color1;
        activeDotColor = content.style.color2;
        try {
            stepTime = Integer.parseInt(content.style.time);
            if (0 < stepTime && stepTime < 1000) {
                stepTime = 1000;
            } else if (1000 < stepTime && stepTime < 2000) {
                stepTime = 2000;
            }
            defAlpha = Integer.parseInt(content.style.opacity1);
            activeAlpha = Integer.parseInt(content.style.opacity2);

        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置模块的背景，padding
     */

    @Override
    public void setModuleBackgroundAndPadding(ModuleBeanCms<ModuleItemBannerBean> moduleBean) {
        // 背景
        setMiddleUI(moduleBean.content, moduleBean.content.list);
        setHotkeyUI(moduleBean.content);
        setBannerUi(moduleBean.content);

    }

    // 设置热词的数据
    private void setHotkeyData(ModuleContent content) {
        if (mHotDataList != null && mHotDataList.size() > 0) {
            mHotDataList.clear();
        }
        mHotDataList.addAll(content.listHostSearch);
        hotSearchAdapter.notifyDataSetChanged();
    }

    // 设置热词的item文字颜色、热词左侧标题文案
    private void setHotkeyUI(ModuleContent content) {
        hotSearchAdapter.setItemTextColor(content.default_icon_color);
        tvHotTittle.setBackgroundDrawable(null);
        tvHotTittle.setText(content.search_text + " ：");
        tvHotTittle.setTextColor(getColor(content.default_icon_color));

    }

    @Override
    public boolean supportSetHei() {
        return false;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_hot_key_and_banner_cms;
    }

    @Override
    public void setStyle(int style) {
        if (style <= 0) {
            style = 91;
        }
        this.style = style;
        layoutStyle = getAttr(1);
        showDog = getAttr(2) != 1;
    }

    @Override
    public void setImageView(ImageView view, ModuleItemBannerBean bean) {
        try {
            if (TextUtils.isEmpty(bean.image)) {
                return;
            } else {
                ImageUtil.load(getContext(), getImgUrl(bean.image), view);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean needUpdateItem(ModuleBeanCms<ModuleItemBannerBean> moduleBean, List<ModuleItemBannerBean> items) {

        boolean keepHotList = false;
        if (this.content != null && moduleBean.content != null && this.content.listHostSearch != null && moduleBean.content.listHostSearch != null && this.content.listHostSearch.size() == moduleBean.content.listHostSearch.size()) {
            for (int a = 0; a < content.listHostSearch.size(); a++) {
                if (!this.moduleBean.content.listHostSearch.get(a).getKeyword().equalsIgnoreCase(moduleBean.content.listHostSearch.get(a).getKeyword())) {
                    keepHotList = false;
                    break;
                }
            }
        } else {
            keepHotList = false;
        }
        if (!keepHotList) {
            if (moduleBean.content.listHostSearch != null && moduleBean.content.listHostSearch.size() > 0) {
                setHotkeyData(moduleBean.content);
            }
        }

        // 什么条件下不更换呢？都为空或者是相等
        if ((content.top_bgRes == null && moduleBean.content.top_bgRes == null) || (content.top_bgRes != null && content.top_bgRes.equals(moduleBean.content.top_bgRes))
                && (content.top_bgRes == null && moduleBean.content.top_bgRes == null) || (content.top_bgRes != null && content.top_bgRes.equals(moduleBean.content.top_bgRes))
                && (content.meddle_bgRes == null && moduleBean.content.meddle_bgRes == null) || (content.meddle_bgRes != null && content.meddle_bgRes.equals(moduleBean.content.meddle_bgRes))
                && (content.bottom_bgRes == null && moduleBean.content.bottom_bgRes == null) || (content.bottom_bgRes != null && content.bottom_bgRes.equals(moduleBean.content.bottom_bgRes))) {
            // 不更换，什么都不做
        } else {
            setMiddleUI(moduleBean.content, items);
        }
        if (!TextUtils.isEmpty(content.search_text) && (!content.search_text.equals(moduleBean.content.search_text) || !content.default_icon_color.equalsIgnoreCase(moduleBean.content.default_icon_color))) {
            setHotkeyUI(moduleBean.content);
        }
        if ((content.style.color1 != null && !content.style.color1.equalsIgnoreCase(moduleBean.content.style.color1)) ||
                (content.style.color2 != null && !content.style.color2.equalsIgnoreCase(moduleBean.content.style.color2)) ||
                (content.style.time != null && !content.style.time.equalsIgnoreCase(moduleBean.content.style.time)) ||
                (content.style.opacity1 != null && !content.style.opacity1.equalsIgnoreCase(moduleBean.content.style.opacity1)) ||
                (content.style.opacity2 != null && !content.style.opacity2.equalsIgnoreCase(moduleBean.content.style.opacity2))) {
            setBannerUi(moduleBean.content);
        }

        // 更新轮播状态
        if (!TextUtils.isEmpty(content.style.time) && !content.style.time.equalsIgnoreCase(moduleBean.content.style.time)){
            try {
                stepTime = Integer.parseInt(moduleBean.content.style.time);
                if (stepTime == 0) {
                    setAutoRoll(false);
                } else {
                    setAutoRoll(true);
                }
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }

        if (items == null || items.size() == 0) {
            mRlLayout.setVisibility(GONE);
        } else {
            mRlLayout.setVisibility(VISIBLE);
        }

        boolean keepOld = true;
        if (this.content != null && moduleBean.content != null && this.content.list != null && moduleBean.content.list != null && this.content.list.size() == moduleBean.content.list.size()) {

            for (int a = 0; a < content.list.size(); a++) {

                if (!this.moduleBean.content.list.get(a).equals(items.get(a))) {
                    keepOld = false;
                    break;
                }
            }
        } else {
            keepOld = false;
        }
        return !keepOld;

        //return super.needUpdateItem(moduleBean,items);
    }

    public void setAutoRoll(boolean autoRoll) {
        if (stepTime == 0 && autoRoll) {
            return;
        }
        this.autoRoll = autoRoll;
        if (!autoRoll) {
            handler.removeCallbacks(showNextPager);
        }
        handler.postDelayed(showNextPager, stepTime);
    }

    public Runnable showNextPager = new Runnable() {

        @Override
        public void run() {
            // 防止重复执行
            handler.removeCallbacks(this);
            // 在进行任务的时候，去判断是否允许，允许才切页面，才定时更新
            if (!autoRoll) {
                return;
            }
            showNextPager();
            handler.postDelayed(this, stepTime);
        }

    };

    /**
     * true 从左向右移动
     */
    private void showNextPager() {
        int currentItem = vp_arl.getCurrentItem();
        if (currentItem == 0) {
            isRight = true;
        } else if (currentItem == adapter.getCount() - 1) {
            isRight = false;
        }
        // 当从左向右移动   角标 +1  ，否则 角标 -1
        int nextIndex = isRight ? currentItem + 1 : currentItem - 1;

        vp_arl.setCurrentItem(nextIndex);
    }


    public ViewPager.OnPageChangeListener PageListener = new ViewPager.OnPageChangeListener() {

        @Override
        public void onPageSelected(int position) {
            // 改变点的状态
            int size = items.size();

            // 改变点的状态
            if (size > 1 && showDog) {
                for (int i = 0; i < size; i++) {
                    ll_arl.getChildAt(i).setEnabled(i != position % size);
                    if (!TextUtils.isEmpty(activeDotColor)) {
                        ll_arl.getChildAt(i).setBackgroundColor(i != position % size ? getColor(defDotColor) : getColor(activeDotColor));
                        ll_arl.getChildAt(i).setAlpha(i != position % size ? defAlpha / 100.0F : activeAlpha / 100.0F);
                    } else {
                        ll_arl.getChildAt(i).setBackgroundColor(i != position % size ? getColor("#b3ffffff") : getColor(defaultTopColor));
                    }
                }
            }

            if (size > 0 && !isImageBgOutside(content)) {

                int index = position % size;
                ivBannerBg.setBackgroundColor(getColor(items.get(index).bgRes));
                llHotSearch.setBackgroundColor(getColor(items.get(index).bgRes));
                ivBannerRestBg.setBackgroundColor(getColor(items.get(index).rest_bgRes));

                currentBgRes = items.get(index).bgRes;
                //LogUtlis.e("xyd", "bgres = " + items.get(index).bgRes + "; rest_bgRes = " + items.get(index).rest_bgRes + "; image = " + items.get(index).image);
                if (mBannerChangeListener != null && canScroll) {
                    mBannerChangeListener.onBannerChanged(getColor(items.get(index).bgRes));
                    //Log.e("xyd", "设置外部背景颜色 canscroll = " + canScroll);
                } else {
                    //LogUtlis.e("xyd","取消search背景轮播");
                }
            }
        }

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

        }

        @Override
        public void onPageScrollStateChanged(int state) {

        }
    };

    /**
     * 外部背景是图片
     *
     * @param content
     * @return
     */
    private boolean isImageBgOutside(ModuleContent content) {

        if (!TextUtils.isEmpty(content.top_bgRes)
                && !TextUtils.isEmpty(content.hotWord_bgRes)
                && !TextUtils.isEmpty(content.meddle_bgRes)
                && !TextUtils.isEmpty(content.bottom_bgRes)
                && content.top_bgRes.startsWith("http")
                && content.hotWord_bgRes.startsWith("http")
                && content.meddle_bgRes.startsWith("http")
                && content.bottom_bgRes.startsWith("http")
        ) {
            return true;
        } else {
            return false;
        }
    }

    private void addDots() {
        if (items == null) {
            return;
        }
        if (items.size() == 1) {
            return;
        }
        for (int i = 0; i < items.size(); i++) {
            // 把10dp 转成对应的像素
            View view = new View(getContext());

            int dotWidth = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 18, getResources().getDisplayMetrics());

            int dotHight = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 3, getResources().getDisplayMetrics());

            // 设置宽高、marging
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(dotWidth, dotHight);
            if (i != items.size() - 1) {
                params.setMargins(0, 0, dotHight, 0);
            }
            view.setLayoutParams(params);
            // 指定背景是选择器，在pagechangelistener中只去改变状态，更加面向对象，易于控制
//            view.setBackgroundResource(R.drawable.arl_ball_bg_selector02);
            ll_arl.addView(view);
        }

    }

    public class MyPagerAdapter extends RecyclingPagerAdapter {

        @Override
        public int getCount() {
            return items == null ? 0 : items.size() > 1 ? Integer.MAX_VALUE : items.size();
        }

        @Override
        public View getView(int position, View convertView, ViewGroup container) {
            final int index = position % items.size();
            ImageView imageView;
            if (convertView == null) {
                ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                imageView = new ImageView(getContext());
                imageView.setScaleType(ImageView.ScaleType.FIT_XY);
                imageView.setLayoutParams(layoutParams);
            } else {
                imageView = (ImageView) convertView;
            }


            setImageView(imageView, items.get(index));
            // banner埋点地方
            imageView.setTag(R.id.tag_action, items.get(index).action);
            imageView.setTag(R.id.tag_1, position);
            imageView.setTag(R.id.tag_2, index);
            imageView.setTag(R.id.tag_banner_type,items.get(index).bannerType);
            Activity context = (Activity) getContext();
            if (context.getClass().getName().contains("ClinicActivity")) {
                imageView.setTag(R.id.tag_click_type, XyyIoUtil.ACTION_CLINIC_BANNER);
            } else if (context.getClass().getName().contains("MainActivity")) {
                imageView.setTag(R.id.tag_click_type, XyyIoUtil.ACTION_HOME_BANNER);
            }
            imageView.setOnClickListener(itemClick);
            return imageView;
        }

    }

    //获取style
    private int getAttr(int index) {
        try {
            return Integer.parseInt(String.valueOf(style).substring(index, index + 1));
        } catch (Throwable e) {
            return 0;
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        setAutoRoll(false);
    }

    @Override
    public void onResume() {
        super.onResume();
        setAutoRoll(true);
    }

    private BannerChangeListener mBannerChangeListener;

    public void setBannerChangeListener(BannerChangeListener l) {
        this.mBannerChangeListener = l;
    }

    public void setCanScroll(boolean canScroll) {
        this.canScroll = canScroll;
        if (mBannerChangeListener != null && canScroll) {
            mBannerChangeListener.onBannerChanged(getColor(currentBgRes));
        }
    }

    public interface BannerChangeListener {
        void onBannerChanged(int color);
    }

}
