package com.ybmmarket20.utils.fileTypeUtil

import android.text.TextUtils
import java.util.*

/**
 * 文件类型
 */
enum class FileTypeEnum(var fileTypeName: String, var magicNumberCode: String) {

    XLS("XLS", "D0CF11E0"),
    DOC("DOC", "D0CF11E0"),
    DOCX("DOCX", "504B0304"),
    XLSX("XLSX", "504B0304"),
    PDF("PDF", "255044462D312E"),
    NOT_EXITS_ENUM("", "");

    companion object {
        /**
         * 根据文件类型获取文件类型魔数编码
         * 默认返回标准件
         *
         * @param magicNumberCode - 文件类型魔数编码
         * @return
         */
        fun getByMagicNumberCode(magicNumberCode: String): FileTypeEnum {
            if (!TextUtils.isEmpty(magicNumberCode)) {
                for (type in values()) {
                    if (magicNumberCode.toUpperCase(Locale.ROOT).startsWith(type.magicNumberCode)) {
                        return type
                    }
                }
            }
            return NOT_EXITS_ENUM
        }

        /**
         * 根据文件类型后缀名获取枚举
         *
         * @param fileTypeName - 文件类型后缀名
         * @return
         */
        fun getByFileTypeName(fileTypeName: String?): FileTypeEnum {
            if (!TextUtils.isEmpty(fileTypeName)) {
                for (type in values()) {
                    if (type.fileTypeName == fileTypeName) {
                        return type
                    }
                }
            }
            return NOT_EXITS_ENUM
        }

        /**
         * 是否存在文件名后缀
         */
        fun isContainSuffixWithDot(fileName: String?): Boolean {
            if (TextUtils.isEmpty(fileName)) return false
            values().forEach {
                if (fileName!!.endsWith(".${it.fileTypeName.toLowerCase(Locale.ROOT)}")) return true
            }
            return false
        }
    }

}