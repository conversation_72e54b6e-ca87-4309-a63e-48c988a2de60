package com.ybmmarket20.utils;

import android.media.ExifInterface;
import android.text.TextUtils;

import java.io.IOException;

public class ExifUtil {
    public static ExifInterface getExifInfo(String imgPath){
        ExifInterface exif= null;
        try {
            exif = new ExifInterface(imgPath);
        } catch (IOException e) {
            e.printStackTrace();
            return  null;
        }
//        HashMap<String, String> attributes = (HashMap<String, String>) ReflectUtil.readField(exif,"mAttributes");
//        for(String string:attributes.keySet()){
//            Log.e("Exif_Info:",string+":"+attributes.get(string));
//        }
        return exif;
    }

    public static void saveExifInfo(String imgPath, ExifInterface exif){
        ExifInterface newExifInfo = getExifInfo(imgPath);
        if (newExifInfo != null && exif != null){
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_APERTURE))) newExifInfo.setAttribute(ExifInterface.TAG_APERTURE,exif.getAttribute(ExifInterface.TAG_APERTURE));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_DATETIME)))newExifInfo.setAttribute(ExifInterface.TAG_DATETIME,exif.getAttribute(ExifInterface.TAG_DATETIME));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_EXPOSURE_TIME)))newExifInfo.setAttribute(ExifInterface.TAG_EXPOSURE_TIME,exif.getAttribute(ExifInterface.TAG_EXPOSURE_TIME));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_FLASH)))newExifInfo.setAttribute(ExifInterface.TAG_FLASH,exif.getAttribute(ExifInterface.TAG_FLASH));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_FOCAL_LENGTH)))newExifInfo.setAttribute(ExifInterface.TAG_FOCAL_LENGTH,exif.getAttribute(ExifInterface.TAG_FOCAL_LENGTH));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_GPS_ALTITUDE)))newExifInfo.setAttribute(ExifInterface.TAG_GPS_ALTITUDE,exif.getAttribute(ExifInterface.TAG_GPS_ALTITUDE));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_GPS_ALTITUDE_REF)))newExifInfo.setAttribute(ExifInterface.TAG_GPS_ALTITUDE_REF,exif.getAttribute(ExifInterface.TAG_GPS_ALTITUDE_REF));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_GPS_DATESTAMP)))newExifInfo.setAttribute(ExifInterface.TAG_GPS_DATESTAMP,exif.getAttribute(ExifInterface.TAG_GPS_DATESTAMP));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_GPS_LATITUDE)))newExifInfo.setAttribute(ExifInterface.TAG_GPS_LATITUDE,exif.getAttribute(ExifInterface.TAG_GPS_LATITUDE));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_GPS_LATITUDE_REF)))newExifInfo.setAttribute(ExifInterface.TAG_GPS_LATITUDE_REF,exif.getAttribute(ExifInterface.TAG_GPS_LATITUDE_REF));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_GPS_LONGITUDE)))newExifInfo.setAttribute(ExifInterface.TAG_GPS_LONGITUDE,exif.getAttribute(ExifInterface.TAG_GPS_LONGITUDE));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF)))newExifInfo.setAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF,exif.getAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_GPS_PROCESSING_METHOD)))newExifInfo.setAttribute(ExifInterface.TAG_GPS_PROCESSING_METHOD,exif.getAttribute(ExifInterface.TAG_GPS_PROCESSING_METHOD));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_GPS_TIMESTAMP)))newExifInfo.setAttribute(ExifInterface.TAG_GPS_TIMESTAMP,exif.getAttribute(ExifInterface.TAG_GPS_TIMESTAMP));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_IMAGE_LENGTH)))newExifInfo.setAttribute(ExifInterface.TAG_IMAGE_LENGTH,exif.getAttribute(ExifInterface.TAG_IMAGE_LENGTH));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_IMAGE_WIDTH)))newExifInfo.setAttribute(ExifInterface.TAG_IMAGE_WIDTH,exif.getAttribute(ExifInterface.TAG_IMAGE_WIDTH));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_ISO)))newExifInfo.setAttribute(ExifInterface.TAG_ISO,exif.getAttribute(ExifInterface.TAG_ISO));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_MAKE)))newExifInfo.setAttribute(ExifInterface.TAG_MAKE,exif.getAttribute(ExifInterface.TAG_MAKE));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_MODEL)))newExifInfo.setAttribute(ExifInterface.TAG_MODEL,exif.getAttribute(ExifInterface.TAG_MODEL));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_ORIENTATION))) newExifInfo.setAttribute(ExifInterface.TAG_ORIENTATION,exif.getAttribute(ExifInterface.TAG_ORIENTATION));
            if(!TextUtils.isEmpty(exif.getAttribute(ExifInterface.TAG_WHITE_BALANCE)))newExifInfo.setAttribute(ExifInterface.TAG_WHITE_BALANCE,exif.getAttribute(ExifInterface.TAG_WHITE_BALANCE));
            try {
                newExifInfo.saveAttributes();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void copyExif(String oldImg, String newImg) {
        saveExifInfo(newImg,getExifInfo(oldImg));
    }
}
