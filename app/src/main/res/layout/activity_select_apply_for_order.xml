<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#EFEFEF"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <RelativeLayout
        android:id="@+id/rl_protect_price"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@color/colors_fff7ef"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:paddingRight="10dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="提示：自客户下单之日起30天内可享受保价护航"
            android:textColor="@color/colors_99664D"
            android:textSize="12sp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:drawablePadding="3dp"
            android:paddingBottom="6dp"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:paddingTop="6dp"
            android:text="查看规则 >"
            android:textColor="@color/colors_99664D"
            android:textSize="10sp" />

    </RelativeLayout>

    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>