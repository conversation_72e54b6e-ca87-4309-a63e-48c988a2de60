<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dimen_dp_10"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="@dimen/dimen_dp_6">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_15"
        android:textColor="#292933"
        android:textStyle="bold"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_13"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="输入卡号添加" />

    <EditText
        android:id="@+id/etBankCardNum"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:hint="点击输入本人银行卡号"
        android:maxLength="30"
        android:textSize="@dimen/dimen_dp_14"
        android:paddingStart="@dimen/dimen_dp_13"
        android:digits="**********"
        android:inputType="number"
        android:textCursorDrawable="@drawable/shape_add_input_card_num_cursor"
        android:background="@drawable/shape_input_bank_num"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <ImageView
        android:id="@+id/ivUserNameClear"
        android:layout_width="@dimen/dimen_dp_44"
        android:layout_height="@dimen/dimen_dp_44"
        android:src="@drawable/clear_sousou"
        android:visibility="gone"
        android:padding="@dimen/dimen_dp_14"
        app:layout_constraintBottom_toBottomOf="@+id/etBankCardNum"
        app:layout_constraintEnd_toEndOf="@+id/etBankCardNum"
        app:layout_constraintTop_toTopOf="@+id/etBankCardNum"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tVBankCardNum"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:text="点击输入本人银行卡号"
        android:textSize="@dimen/dimen_dp_14"
        android:paddingStart="@dimen/dimen_dp_13"
        android:gravity="center_vertical"
        android:background="@drawable/shape_set_pay_pw_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/rtvNext"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:text="下一步"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_16"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etBankCardNum"
        app:rv_backgroundColor="@color/color_00b377"
        app:rv_cornerRadius="@dimen/dimen_dp_2" />


</com.ybmmarket20.common.widget.RoundConstraintLayout>