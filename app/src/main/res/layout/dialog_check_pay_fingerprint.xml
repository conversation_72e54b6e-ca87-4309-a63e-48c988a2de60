<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="@dimen/dimen_dp_19"
        android:layout_height="@dimen/dimen_dp_19"
        android:layout_marginStart="@dimen/dimen_dp_16"
        android:layout_marginTop="@dimen/dimen_dp_16"
        android:src="@drawable/icon_pay_fingerprint_dialog_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvUserPW"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_dp_16"
        android:text="使用密码"
        android:textColor="@color/color_00b377"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintBottom_toBottomOf="@+id/ivBack"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivBack" />

    <TextView
        android:id="@+id/tvPayFingerprintTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_65"
        android:text="请验证指纹"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_16"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivPayFingerprintBottom"
        android:layout_width="@dimen/dimen_dp_44"
        android:layout_height="@dimen/dimen_dp_44"
        android:src="@drawable/icon_fingerprint_bottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/dimen_dp_140"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/dimen_dp_60" />

</androidx.constraintlayout.widget.ConstraintLayout>