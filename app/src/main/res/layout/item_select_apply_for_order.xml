<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="164dp"
    android:layout_marginTop="10dp"
    android:paddingLeft="10dp"
    android:paddingRight="10dp">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:id="@+id/fg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:orientation="vertical"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="6dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:background="@color/white"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_order_no"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="3dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="#FF292933"
                android:textSize="15sp"
                tools:text="20160321100912312" />

        </LinearLayout>

        <com.ybmmarket20.common.widget.RoundRelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:paddingBottom="10dp"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            app:rv_backgroundColor="#FFFBFBFB"
            app:rv_cornerRadius_BL="6dp"
            app:rv_cornerRadius_BR="6dp">

            <com.ybmmarket20.common.widget.RoundedImageView
                android:id="@+id/iv_order"
                android:layout_width="99dp"
                android:layout_height="100dp"
                android:layout_centerVertical="true"
                android:padding="5dp"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="3dp"
                app:rv_strokeColor="@color/divider_line_base_1px"
                app:rv_strokeWidth="0.5dp" />

            <TextView
                android:id="@+id/tv_order_number"
                android:layout_width="99dp"
                android:layout_height="22dp"
                android:layout_alignParentBottom="true"
                android:background="@drawable/gray_bottom_radius_5dp_bg"
                android:gravity="center"
                android:text="4件药品"
                android:textColor="@color/white"
                android:textSize="@dimen/check_order_tv2" />

            <RelativeLayout
                android:id="@+id/ll_order_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_toRightOf="@+id/iv_order"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_order_total"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text=""
                    android:textColor="#FF292933"
                    android:textSize="17sp"
                    tools:text="总计：¥450.00" />

                <TextView
                    android:id="@+id/tv_order_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_order_total"
                    android:layout_marginTop="2dp"
                    android:textColor="#FF9494A6"
                    android:textSize="14sp"
                    tools:text="2016-3-21 10:09" />

                <TextView
                    android:id="@+id/tv_order_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_marginBottom="10dp"
                    android:textColor="#FF292933"
                    android:textSize="15sp"
                    tools:text="出库中"
                    tools:visibility="visible" />

            </RelativeLayout>

            <ImageView
                android:layout_width="8dp"
                android:layout_height="16dp"
                android:layout_alignParentRight="true"
                android:layout_marginRight="10dp"
                android:layout_marginTop="10dp"
                android:src="@drawable/icon_arrow_black" />

        </com.ybmmarket20.common.widget.RoundRelativeLayout>

    </com.ybmmarket20.common.widget.RoundLinearLayout>

</RelativeLayout>