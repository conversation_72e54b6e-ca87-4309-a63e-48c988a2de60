<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp_arl"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </androidx.viewpager.widget.ViewPager>

    <ImageView
        android:id="@+id/iv_brand_mark"
        android:layout_width="225dp"
        android:layout_height="225dp"
        android:layout_centerInParent="true"
        android:scaleType="fitXY"
        android:src="@drawable/transparent" />

    <com.ybmmarket20.view.PromotionTagView
        android:id="@+id/view_ptv"
        android:layout_width="225dp"
        android:layout_height="225dp"
        android:layout_centerInParent="true"
        app:contentTextSize="15dp"
        app:shopBottomTextSize="15dp"
        app:shopContentTextSize="22.5dp"
        app:shopTimeTextSize="15dp"
        app:shopTopTextSize="17.5dp"
        app:subTitleTextSize="9dp" />

    <LinearLayout
        android:id="@+id/ll_arl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="21dp"
        android:orientation="horizontal"
        android:paddingLeft="4dp"
        android:paddingTop="5dp"
        android:paddingRight="4dp"
        android:paddingBottom="5dp" />

    <LinearLayout
        android:id="@+id/ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="20.5dp"
        android:layout_marginBottom="@dimen/dimen_dp_9"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_pager"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="1dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="111"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_pager2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="12sp" />

    </LinearLayout>

</RelativeLayout>
