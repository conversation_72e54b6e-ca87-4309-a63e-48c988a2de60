<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_search"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_code"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_gravity="center_vertical"
        android:layout_margin="10dp"
        android:scaleType="center"
        android:src="@drawable/icon_search_scan_02" />

    <RelativeLayout
        android:id="@+id/home_search_rl"
        android:layout_width="0dp"
        android:layout_height="34dp"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="8dp"
        android:layout_weight="1"
        android:background="@drawable/search_round_corner_gray_bg_03">

        <ImageView
            android:id="@+id/iv_a_magnifying_glass"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:src="@drawable/icon_a_magnifying_glass" />

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="3dp"
            android:layout_toRightOf="@+id/iv_a_magnifying_glass"
            android:background="@null"
            android:maxLines="1"
            android:singleLine="true"
            android:text="@string/search_hint"
            android:textColor="@color/color_9494A6"
            android:textSize="13dp" />

        <ImageView
            android:id="@+id/iv_voice"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="6dp"
            android:src="@drawable/nav_voice_01" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/title_left"
        android:layout_width="44dp"
        android:layout_height="22dp">

        <ImageView
            android:layout_width="@dimen/dimen_dp_22"
            android:layout_height="@dimen/dimen_dp_22"
            android:layout_centerInParent="true"
            android:layout_marginLeft="15dp"
            android:scaleType="center"
            android:src="@drawable/icon_search_message_02" />

        <TextView
            android:id="@+id/tv_smg_num"
            style="@style/more_msg_tip_style"
            android:text="1"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_smg_num_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/tv_smg_num"
            android:layout_alignRight="@id/tv_smg_num"
            android:layout_marginTop="-4dp"
            android:text="+"
            android:textColor="@color/white"
            android:textSize="9sp"
            android:visibility="gone"
            tools:visibility="visible" />


    </RelativeLayout>
</LinearLayout>
