<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_fast_entry_item_bg"
        style="@style/fast_entry_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:cardElevation="@dimen/dimen_dp_0"
        app:cardCornerRadius="@dimen/dimen_dp_5">
        <ImageView
            android:id="@+id/iv_fast_entry_item_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_home_steady_fast_entry_kill"/>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_major_title"
        style="@style/shopping_guide_major_title"
        android:text="@string/shopping_guide_major_title_daily_discount"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/cv_fast_entry_item_bg" />

    <TextView
        android:id="@+id/tv_minor_title"
        style="@style/shopping_guide_minor_title"
        android:alpha="0.3"
        android:text="@string/shopping_guide_minor_title_daily_discount"
        android:textColor="#ff222222"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_major_title" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_item_shopping_guide_bg1"
        style="@style/shopping_guide_img"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:background="@color/white"
        app:cardCornerRadius="@dimen/dimen_dp_8"
        app:cardElevation="@dimen/dimen_dp_0"
        app:layout_constraintEnd_toStartOf="@+id/cv_item_shopping_guide_bg2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title">

        <ImageView
            android:id="@+id/iv_product1"
            style="@style/shopping_guide_img" />
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_item_shopping_guide_bg2"
        style="@style/shopping_guide_img"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:background="@color/white"
        app:cardCornerRadius="@dimen/dimen_dp_8"
        app:cardElevation="@dimen/dimen_dp_0"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cv_item_shopping_guide_bg1"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title">

        <ImageView
            android:id="@+id/iv_product2"
            style="@style/shopping_guide_img"
            app:layout_constraintBottom_toBottomOf="@+id/cv_item_shopping_guide_bg2"
            app:layout_constraintEnd_toEndOf="@+id/cv_item_shopping_guide_bg2"
            app:layout_constraintStart_toStartOf="@+id/cv_item_shopping_guide_bg2"
            app:layout_constraintTop_toTopOf="@+id/cv_item_shopping_guide_bg2" />
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>