<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="6"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clTypeFilter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:layout_marginBottom="@dimen/dimen_dp_10">


            <CheckBox
                android:id="@+id/tv_self_option"
                android:layout_width="83dp"
                android:layout_height="24dp"
                android:layout_marginLeft="@dimen/dimen_dp_15"
                android:background="@drawable/bg_selector_00b377_f7f7f8"
                android:button="@null"
                android:gravity="center"
                android:text="自营"
                android:textColor="@color/selector_text_color_tab_292933"
                android:textSize="@dimen/dimen_dp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:checked="true" />

            <CheckBox
                android:id="@+id/tv_shop_account_status_option"
                android:layout_width="83dp"
                android:layout_height="24dp"
                android:layout_marginLeft="@dimen/dimen_dp_15"
                android:background="@drawable/bg_selector_00b377_f7f7f8"
                android:button="@null"
                android:gravity="center"
                android:text="已开户"
                android:textColor="@color/selector_text_color_tab_292933"
                android:textSize="@dimen/dimen_dp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/tv_self_option"
                app:layout_constraintTop_toTopOf="parent" />

            <CheckBox
                    android:id="@+id/tv_shop_quality_status_option"
                    android:layout_width="83dp"
                    android:layout_height="24dp"
                    android:layout_marginLeft="@dimen/dimen_dp_15"
                    android:background="@drawable/bg_selector_00b377_f7f7f8"
                    android:button="@null"
                    android:gravity="center"
                    android:text="品质商家"
                    android:textColor="@color/selector_text_color_tab_292933"
                    android:textSize="@dimen/dimen_dp_12"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toRightOf="@id/tv_shop_account_status_option"
                    app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/dimen_dp_10"
            android:paddingLeft="10dp"
            android:paddingRight="10dp">

            <EditText
                android:id="@+id/et_search"
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:singleLine="true"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_centerVertical="true"
                android:background="@drawable/search_round_corner_gray_bg_03"
                android:drawableLeft="@drawable/manufacturers_search"
                android:drawablePadding="6dp"
                android:hint="搜索商家名字"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:textColor="@color/text_292933"
                android:textColorHint="@color/text_9494A6"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="8dp"
                android:padding="6dp"
                android:src="@drawable/clear_sousou"
                android:visibility="gone"/>
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_shopstore"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:listitem="@layout/item_pop_shopstore"
            android:overScrollMode="never" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_reset"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/bg_filtrate_classify_btn2_reset"
            android:text="重置"
            android:textColor="@color/text_292933"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_affirm"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@color/base_colors_new"
            android:elevation="0dp"
            android:text="确定"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>