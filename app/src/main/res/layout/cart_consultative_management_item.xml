<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingLeft="20dp"
    android:paddingRight="15dp">

    <TextView
        android:id="@+id/tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:minHeight="49dp"
        android:text="111"
        android:textColor="@color/text_292933"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/colors_f5f5f5" />

</LinearLayout>
