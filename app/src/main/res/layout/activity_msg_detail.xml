<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <include layout="@layout/common_header_items" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/activity_bg"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_time"
            android:layout_width="match_parent"
            android:gravity="center"
            android:textSize="15sp"
            android:textColor="@color/text_676773"
            android:layout_height="45dp" />
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:gravity="center_vertical|left"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:background="@color/white"
            android:textSize="17sp"
            android:drawablePadding="2dp"
            android:paddingLeft="6dp"
            android:singleLine="true"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:lineSpacingExtra="2dp"
            android:paddingRight="6dp"
            android:textColor="@color/text_676773"
            android:layout_height="wrap_content" />
        <TextView
            android:id="@+id/tv_content"
            android:includeFontPadding="false"
            android:layout_width="match_parent"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:gravity="left"
            android:paddingTop="8dp"
            android:layout_marginTop="0.5dp"
            android:background="@color/white"
            android:layout_weight="1"
            android:textSize="16sp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:lineSpacingExtra="4dp"
            android:textColor="@color/text_292933"
            android:layout_height="0dp"  />
    </LinearLayout>
</LinearLayout>