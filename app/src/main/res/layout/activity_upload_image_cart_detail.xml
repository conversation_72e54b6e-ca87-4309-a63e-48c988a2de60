<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/activity_bg"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <com.ybmmarket20.view.MyScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_below="@+id/ll_title"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="22dp"
                android:text="图片采购单名称"
                android:textColor="@color/text_9494A6"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_image_cart_detail_name"
                android:padding="10dp"
                android:textColor="@color/text_292933"
                android:textSize="14sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="22dp"
                android:text="请选择上传的图片"
                android:textColor="@color/text_9494A6"
                android:textSize="14sp" />

            <RelativeLayout
                android:id="@+id/fragment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:minHeight="320dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="20dp"
                android:paddingRight="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableLeft="@drawable/icon_hint_image_cart"
                    android:drawablePadding="2dp"
                    android:text="小提示："
                    android:textColor="@color/text_9494A6"
                    android:textSize="14sp"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="每次最多上传9张图片，上传成功后，小蜜会帮你创建电子计划单哦··"
                    android:textColor="@color/text_9494A6"
                    android:textSize="14sp" />

            </LinearLayout>
        </LinearLayout>
    </com.ybmmarket20.view.MyScrollView>

    <LinearLayout
        android:id="@+id/ll_create_list"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:gravity="center">

        <com.ybmmarket20.view.ButtonObserver
            android:id="@+id/btn_ok"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="45dp"
            android:gravity="center"
            android:layout_gravity="center_vertical"
            android:layout_margin="8dp"
            android:background="@drawable/bg_btn_upload_cart_detail2"
            android:text="上传采购单"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>