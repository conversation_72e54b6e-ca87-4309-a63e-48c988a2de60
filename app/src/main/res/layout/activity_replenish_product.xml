<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f8f8f8"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <TextView
        android:id="@+id/tv_failed_tip"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:background="@color/text_replenishment_FFF390"
        android:gravity="center"
        android:text="@string/text_replenishment_hint"
        android:textColor="@color/text_replenishment_FC6E00"
        android:textSize="14sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:orientation="vertical"
        android:paddingLeft="10dp"
        android:paddingRight="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <TextView
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="名称："
                android:textColor="#AAAAAA"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/tv_product_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入商品名称"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="#292933"
                android:textColorHint="#9494A6"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_search"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:text="搜索"
                android:textColor="#00B377"
                android:textSize="16sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#eeeeee" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <TextView
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="规格："
                android:textColor="#AAAAAA"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/tv_product_spec"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="#292933"
                android:textColorHint="#9494A6"
                android:textSize="16sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#eeeeee" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <TextView
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="厂家："
                android:textColor="#AAAAAA"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/tv_manufacturer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="#292933"
                android:textColorHint="#9494A6"
                android:textSize="16sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#eeeeee" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <TextView
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="采购数量："
                android:textColor="#AAAAAA"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_product_num"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:cursorVisible="true"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:inputType="number"
                android:maxLength="7"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="#292933"
                android:textColorHint="#9494A6"
                android:textCursorDrawable="@drawable/color_cursor"
                android:textSize="16sp" />
        </LinearLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#eeeeee" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <TextView
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="历史购进价："
                android:textColor="#AAAAAA"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_product_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:cursorVisible="true"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:numeric="decimal"
                android:maxLength="7"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="#292933"
                android:textColorHint="#9494A6"
                android:textCursorDrawable="@drawable/color_cursor"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_add_plan"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_margin="16dp"
        android:background="@drawable/bg_rect_radio_green_shape"
        android:gravity="center"
        android:text="添加到补货登记"
        android:textColor="@color/white"
        android:textSize="16sp" />

</LinearLayout>
