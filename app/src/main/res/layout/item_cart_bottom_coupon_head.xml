<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dimen_dp_15">

    <TextView
        android:id="@+id/tv_cart_bottom_coupon_head_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_16"
        android:textColor="@color/color_292933"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="可领取优惠券" />

    <TextView
        android:id="@+id/tv_cart_bottom_coupon_head_des"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:textColor="@color/detail_tv_575766"
        android:textSize="13sp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_cart_bottom_coupon_head_title"
        app:layout_constraintStart_toEndOf="@+id/tv_cart_bottom_coupon_head_title"
        tools:text="领取后可用于购物车商品" />
</androidx.constraintlayout.widget.ConstraintLayout>