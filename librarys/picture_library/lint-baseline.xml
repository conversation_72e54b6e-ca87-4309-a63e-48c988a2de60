<?xml version="1.0" encoding="UTF-8"?>
<issues format="5" by="lint 3.6.3" client="gradle" variant="release" version="3.6.3">

    <issue
        id="ObsoleteLintCustomCheck"
        message="<PERSON><PERSON> found an issue registry (`androidx.appcompat.AppCompatIssueRegistry`) which requires a newer API level. That means that the custom lint checks are intended for a newer lint version; please upgrade">
        <location
            file="../../../../../.gradle/caches/transforms-2/files-2.1/4bcbea224e1f5a97ce3a89a5d02de9cd/appcompat-1.2.0/jars/lint.jar"/>
    </issue>

    <issue
        id="CheckResult"
        message="The result of `subscribe` is not used"
        errorLine1="            Flowable.just(result)"
        errorLine2="            ^">
        <location
            file="src/main/java/com/luck/picture/lib/PictureBaseActivity.java"
            line="208"
            column="13"/>
    </issue>

    <issue
        id="ExifInterface"
        message="Avoid using `android.media.ExifInterface`; use `android.support.media.ExifInterface` from the support library instead"
        errorLine1="import android.media.ExifInterface;"
        errorLine2="       ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/BitmapLoadUtils.java"
            line="9"
            column="8"/>
    </issue>

    <issue
        id="ExifInterface"
        message="Avoid using `android.media.ExifInterface`; use `android.support.media.ExifInterface` from the support library instead"
        errorLine1="import android.media.ExifInterface;"
        errorLine2="       ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/PictureFileUtils.java"
            line="16"
            column="8"/>
    </issue>

    <issue
        id="ExifInterface"
        message="Avoid using `android.media.ExifInterface`; use `android.support.media.ExifInterface` from the support library instead"
        errorLine1="import android.media.ExifInterface;"
        errorLine2="       ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/widget/longimage/SubsamplingScaleImageView.java"
            line="33"
            column="8"/>
    </issue>

    <issue
        id="PrivateApi"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future"
        errorLine1="            Class&lt;?> layoutParams = Class.forName(&quot;android.view.MiuiWindowManager$LayoutParams&quot;);"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/immersive/LightStatusBarUtils.java"
            line="59"
            column="37"/>
    </issue>

    <issue
        id="PrivateApi"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future"
        errorLine1="            c = Class.forName(&quot;com.android.internal.R$dimen&quot;);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/ScreenUtils.java"
            line="36"
            column="17"/>
    </issue>

    <issue
        id="SimpleDateFormat"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates."
        errorLine1="    private static SimpleDateFormat msFormat = new SimpleDateFormat(&quot;mm:ss&quot;);"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/DateUtils.java"
            line="14"
            column="48"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        window = LayoutInflater.from(context).inflate(R.layout.picture_window_folder, null);"
        errorLine2="                                                                                      ~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/widget/FolderPopWindow.java"
            line="55"
            column="87"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        View inflate = LayoutInflater.from(context).inflate(R.layout.picture_camera_pop_layout, null);"
        errorLine2="                                                                                                ~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/widget/PhotoPopupWindow.java"
            line="36"
            column="97"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of io.reactivex.rxjava2:rxjava than 2.1.3 is available: 2.2.6"
        errorLine1="    implementation &quot;io.reactivex.rxjava2:rxjava:2.1.3&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="41"
            column="5"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of io.reactivex.rxjava2:rxandroid than 2.0.1 is available: 2.1.1"
        errorLine1="    implementation &quot;io.reactivex.rxjava2:rxandroid:2.0.1&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="42"
            column="5"/>
    </issue>

    <issue
        id="Typos"
        message="&quot;Ok&quot; is usually capitalized as &quot;OK&quot;"
        errorLine1="    &lt;string name=&quot;picture_done_front_num&quot;>%1$d/%2$d Ok&lt;/string>"
        errorLine2="                                                    ^">
        <location
            file="src/main/res/values-en/strings.xml"
            line="10"
            column="53"/>
    </issue>

    <issue
        id="Typos"
        message="&quot;Ok&quot; is usually capitalized as &quot;OK&quot;"
        errorLine1="    &lt;string name=&quot;picture_done&quot;>Ok&lt;/string>"
        errorLine2="                                ^">
        <location
            file="src/main/res/values-en/strings.xml"
            line="12"
            column="33"/>
    </issue>

    <issue
        id="Recycle"
        message="This `Cursor` should be freed up after use with `#close()`"
        errorLine1="                Cursor data = mContext.getContentResolver().query(QUERY_URI, PROJECTION, selection, selectionArgs, ORDER_BY);"
        errorLine2="                                                            ~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/model/LocalMediaLoader.java"
            line="152"
            column="61"/>
    </issue>

    <issue
        id="Recycle"
        message="This `Cursor` should be freed up after use with `#close()`"
        errorLine1="                    .query(uri, null, null, null, null);"
        errorLine2="                     ~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/PictureBaseActivity.java"
            line="568"
            column="22"/>
    </issue>

    <issue
        id="Recycle"
        message="This `Cursor` should be freed up after use with `#close()`"
        errorLine1="            Cursor query = context.getApplicationContext().getContentResolver().query(Uri.parse(videoPath),"
        errorLine2="                                                                                ~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/config/PictureMimeType.java"
            line="269"
            column="81"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        message="Invalid layout param in a `RelativeLayout`: `layout_weight`"
        errorLine1="                android:layout_weight=&quot;1&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_picture_play_audio.xml"
            line="54"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        message="Invalid layout param in a `RelativeLayout`: `layout_weight`"
        errorLine1="                android:layout_weight=&quot;1&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_audio_dialog.xml"
            line="53"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        message="Invalid layout param in a `FrameLayout`: `layout_centerInParent`"
        errorLine1="        android:layout_centerInParent=&quot;true&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_image_preview.xml"
            line="17"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/BitmapLoadTask.java"
            line="132"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB_MR2) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/BitmapLoadUtils.java"
            line="132"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        if (VERSION.SDK_INT >= VERSION_CODES.JELLY_BEAN) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/photoview/Compat.java"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/EglUtils.java"
            line="29"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        final boolean isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT;"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/FileUtils.java"
            line="140"
            column="34"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is never &lt; 21"
        errorLine1="                if (Build.VERSION.SDK_INT &lt;= Build.VERSION_CODES.JELLY_BEAN) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/widget/FolderPopWindow.java"
            line="132"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT &amp;&amp; Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/immersive/ImmersiveManage.java"
            line="54"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is never &lt; 21"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT &amp;&amp; Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/immersive/ImmersiveManage.java"
            line="54"
            column="72"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/immersive/LightStatusBarUtils.java"
            line="163"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is never &lt; 21"
        errorLine1="                if (Build.VERSION.SDK_INT &lt;= Build.VERSION_CODES.JELLY_BEAN) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/widget/PhotoPopupWindow.java"
            line="94"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is never &lt; 21"
        errorLine1="        boolean compare_SDK_19 = Build.VERSION.SDK_INT &lt;= Build.VERSION_CODES.KITKAT;"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/PictureBaseActivity.java"
            line="539"
            column="34"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        final boolean isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT;"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/PictureFileUtils.java"
            line="228"
            column="34"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/SdkVersionUtils.java"
            line="21"
            column="16"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/SdkVersionUtils.java"
            line="25"
            column="16"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        if (parallelLoadingEnabled &amp;&amp; VERSION.SDK_INT >= 11) {"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/widget/longimage/SubsamplingScaleImageView.java"
            line="1821"
            column="39"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="        if (VERSION.SDK_INT >= 14) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/widget/longimage/SubsamplingScaleImageView.java"
            line="1915"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="This field leaks a context object"
        errorLine1="    private final Context mContext;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/tools/BitmapLoadTask.java"
            line="41"
            column="5"/>
    </issue>

    <issue
        id="HandlerLeak"
        message="This Handler class should be static or leaks might occur (anonymous android.os.Handler)"
        errorLine1="    private Handler handler = new Handler() {"
        errorLine2="                              ^">
        <location
            file="src/main/java/com/luck/picture/lib/PictureExternalPreviewActivity.java"
            line="389"
            column="31"/>
    </issue>

    <issue
        id="HandlerLeak"
        message="This Handler class should be static or leaks might occur (anonymous android.os.Handler)"
        errorLine1="    private Handler mHandler = new Handler() {"
        errorLine2="                               ^">
        <location
            file="src/main/java/com/luck/picture/lib/PictureSelectorActivity.java"
            line="97"
            column="32"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/transparent_white` with a theme that also paints a background (inferred theme is `@style/AppTheme`)"
        errorLine1="    android:background=&quot;@color/transparent_white&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_picture_play_audio.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/AppTheme`)"
        errorLine1="    android:background=&quot;@color/black&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_activity_external_preview.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/AppTheme`)"
        errorLine1="    android:background=&quot;@color/black&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_activity_video_play.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#70000000` with a theme that also paints a background (inferred theme is `@style/AppTheme`)"
        errorLine1="    android:background=&quot;#70000000&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_camera_pop_layout.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/transparent` with a theme that also paints a background (inferred theme is `@style/AppTheme`)"
        errorLine1="    android:background=&quot;@color/transparent&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_empty.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/AppTheme`)"
        errorLine1="    android:background=&quot;@color/black&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_preview.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/AppTheme`)"
        errorLine1="    android:background=&quot;@color/white&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_selector.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="UselessParent"
        message="This `LinearLayout` layout or its `FrameLayout` parent is useless; transfer the `background` attribute to the other view"
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_audio_dialog.xml"
            line="7"
            column="6"/>
    </issue>

    <issue
        id="UselessParent"
        message="This `LinearLayout` layout or its `RelativeLayout` parent is possibly useless; transfer the `background` attribute to the other view"
        errorLine1="        &lt;LinearLayout"
        errorLine2="         ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_preview.xml"
            line="69"
            column="10"/>
    </issue>

    <issue
        id="UselessParent"
        message="This `LinearLayout` layout or its `LinearLayout` parent is useless; transfer the `background` attribute to the other view"
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_wind_base_dialog_xml.xml"
            line="6"
            column="6"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;picture_please&quot;>Please later...&lt;/string>"
        errorLine2="                                  ^">
        <location
            file="src/main/res/values-en/strings.xml"
            line="9"
            column="35"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;picture_please&quot;>请稍候...&lt;/string>"
        errorLine2="                                  ^">
        <location
            file="src/main/res/values-zh/strings.xml"
            line="10"
            column="35"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;picture_please&quot;>请稍候...&lt;/string>"
        errorLine2="                                  ^">
        <location
            file="src/main/res/values/strings.xml"
            line="10"
            column="35"/>
    </issue>

    <issue
        id="IconDensities"
        message="Missing the following drawables in `drawable-xhdpi`: arrow_down.png, arrow_up.png, ic_check.png, ic_checked.png, ic_placeholder.png... (4 more)">
        <location
            file="src/main/res/drawable-xhdpi"/>
    </issue>

    <issue
        id="IconDensities"
        message="Missing the following drawables in `drawable-xxhdpi`: buybuybuy.png, def.png, def_qq.png, ic_camera.png, ic_delete_photo.png... (7 more)">
        <location
            file="src/main/res/drawable-xxhdpi"/>
    </issue>

    <issue
        id="IconMissingDensityFolder"
        message="Missing density variation folders in `src/main/res`: drawable-hdpi, drawable-mdpi">
        <location
            file="src/main/res"/>
    </issue>

    <issue
        id="ButtonOrder"
        message="Cancel button should be on the left (was &quot;Cancel | Cancel&quot;, should be &quot;Cancel | Cancel&quot;)"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="src/main/res/layout/picture_wind_base_dialog_xml.xml"
            line="80"
            column="18"/>
    </issue>

    <issue
        id="ButtonCase"
        message="The standard Android way to capitalize Ok is &quot;OK&quot; (tip: use `@android:string/ok` instead)"
        errorLine1="    &lt;string name=&quot;picture_done&quot;>Ok&lt;/string>"
        errorLine2="                                ^">
        <location
            file="src/main/res/values-en/strings.xml"
            line="12"
            column="33"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view ``ImageView`` has `setOnTouchListener` called on it but does not override `performClick`"
        errorLine1="        imageView.setOnTouchListener(this);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/photoview/PhotoViewAttacher.java"
            line="154"
            column="9"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="`PhotoViewAttacher#onTouch` should call `View#performClick` when a click is detected"
        errorLine1="    public boolean onTouch(View v, MotionEvent ev) {"
        errorLine2="                   ~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/photoview/PhotoViewAttacher.java"
            line="344"
            column="20"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view `PreviewViewPager` overrides `onTouchEvent` but not `performClick`"
        errorLine1="    public boolean onTouchEvent(MotionEvent ev) {"
        errorLine2="                   ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/widget/PreviewViewPager.java"
            line="27"
            column="20"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view `SubsamplingScaleImageView` overrides `onTouchEvent` but not `performClick`"
        errorLine1="    public boolean onTouchEvent(@NonNull MotionEvent event) {"
        errorLine2="                   ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/luck/picture/lib/widget/longimage/SubsamplingScaleImageView.java"
            line="616"
            column="20"/>
    </issue>

    <issue
        id="RelativeOverlap"
        message="`@id/ll_check` can overlap `@id/picture_left_back` if @id/ll_check grows due to localized text expansion"
        errorLine1="        &lt;LinearLayout"
        errorLine2="         ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_preview.xml"
            line="34"
            column="10"/>
    </issue>

    <issue
        id="RelativeOverlap"
        message="`@id/ll_check` can overlap `@id/picture_title` if @id/picture_title, @id/ll_check grow due to localized text expansion"
        errorLine1="        &lt;LinearLayout"
        errorLine2="         ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_preview.xml"
            line="34"
            column="10"/>
    </issue>

    <issue
        id="RelativeOverlap"
        message="`@id/id_ll_ok` can overlap `@id/picture_id_preview` if @string/picture_preview, @id/id_ll_ok grow due to localized text expansion"
        errorLine1="        &lt;LinearLayout"
        errorLine2="         ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/picture_selector.xml"
            line="37"
            column="10"/>
    </issue>

    <issue
        id="RelativeOverlap"
        message="`@id/picture_right` can overlap `@id/picture_left_back` if @string/picture_cancel grows due to localized text expansion"
        errorLine1="        &lt;TextView"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/picture_title_bar.xml"
            line="38"
            column="10"/>
    </issue>

    <issue
        id="RelativeOverlap"
        message="`@id/picture_right` can overlap `@id/picture_title` if @string/picture_camera_roll, @string/picture_cancel grow due to localized text expansion"
        errorLine1="        &lt;TextView"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/picture_title_bar.xml"
            line="38"
            column="10"/>
    </issue>

</issues>
