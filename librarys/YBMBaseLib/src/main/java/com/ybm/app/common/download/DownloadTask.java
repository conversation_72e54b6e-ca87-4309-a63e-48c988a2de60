package com.ybm.app.common.download;

import android.text.TextUtils;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.NetUtil;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeoutException;

import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * Created by dzc on 15/11/21.
 */
public class DownloadTask implements Runnable {

    public String id;
    public long toolSize;
    public long completedSize;         //  Download section has been completed
    //    private float percent;        //  Percent Complete
    public String url;
    public String saveDirPath;
    public boolean alone = false;// true 是单独使用，没有和manager 一起使用
    public RandomAccessFile file;
    private int UPDATE_SIZE = 50 * 1024;    // The database is updated once every 50k
    public int downloadStatus = DownloadStatus.DOWNLOAD_STATUS_INIT;
    public String fileName;    // File name when saving
    public List<DownloadTaskListener> listeners = new ArrayList<>();
    public Object tag;

    public DownloadTask(String id, String url, String saveDirPath, String fileName) {
        this.id = id;
        this.url = url;
        this.saveDirPath = saveDirPath;
        this.fileName = fileName;
    }

    public DownloadTask(String id, String url, String saveDirPath, String fileName, Object tag) {
        this.id = id;
        this.url = url;
        this.saveDirPath = saveDirPath;
        this.fileName = fileName;
        this.tag = tag;
    }

    public DownloadTask() {

    }

    @Override
    public void run() {
        if (NetUtil.getNetworkState(BaseYBMApp.getAppContext()) == NetUtil.NETWORN_NONE) {
            downloadStatus = DownloadStatus.DOWNLOAD_STATUS_ERROR;
            onError(DownloadTaskListener.DOWNLOAD_ERROR_NET_NOT_ERROR);
            return;
        }
        downloadStatus = DownloadStatus.DOWNLOAD_STATUS_PREPARE;
        onPrepare();
        InputStream inputStream = null;
        BufferedInputStream bis = null;
        try {
            File temp = new File(saveDirPath);
            if (!temp.exists()) {
                temp.mkdirs();
            }
            file = new RandomAccessFile(saveDirPath + fileName, "rwd");
            if (file.length() < completedSize) {
                completedSize = file.length();
            }
            long fileLength = file.length();
            if (fileLength != 0 && toolSize <= fileLength) {
                downloadStatus = DownloadStatus.DOWNLOAD_STATUS_COMPLETED;
                toolSize = completedSize = fileLength;
                onCompleted();
                return;
            }
            downloadStatus = DownloadStatus.DOWNLOAD_STATUS_START;
            onStart();
            Request request = new Request.Builder()
                    .url(url)
                    .header("RANGE", "bytes=" + completedSize + "-")    //  Http value set breakpoints RANGE
                    .build();
            file.seek(completedSize);
            Response response = DownloadManager.getInstance().getResponse(request);
            if (response == null || !response.isSuccessful()) {
                downloadStatus = DownloadStatus.DOWNLOAD_STATUS_ERROR;
                onError(DownloadTaskListener.DOWNLOAD_ERROR_API_ERROR);
                return;
            }
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                downloadStatus = DownloadStatus.DOWNLOAD_STATUS_DOWNLOADING;
                if (toolSize <= 0) {
                    toolSize = responseBody.contentLength();
                }
                inputStream = responseBody.byteStream();
                bis = new BufferedInputStream(inputStream);
                byte[] buffer = new byte[2 * 1024];
                int length = 0;
                int buffOffset = 0;
                while ((length = bis.read(buffer)) > 0 && downloadStatus != DownloadStatus.DOWNLOAD_STATUS_CANCEL && downloadStatus != DownloadStatus.DOWNLOAD_STATUS_PAUSE) {
                    file.write(buffer, 0, length);
                    completedSize += length;
                    buffOffset += length;
                    if (buffOffset >= UPDATE_SIZE) {
                        buffOffset = 0;
                        onDownloading();
                    }
                }
                onDownloading();
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            downloadStatus = DownloadStatus.DOWNLOAD_STATUS_ERROR;
            onError(DownloadTaskListener.DOWNLOAD_ERROR_FILE_NOT_FOUND);
            BugUtil.sendBug(new NullPointerException("下载文件找不到：" + saveDirPath + ":" + fileName));
        } catch (Throwable e) {
            e.printStackTrace();
            downloadStatus = DownloadStatus.DOWNLOAD_STATUS_ERROR;
            if (e instanceof SocketException) {
                onError(DownloadTaskListener.DOWNLOAD_ERROR_IO_ERROR_SOCKETEXCEPTION);
            } else if (e instanceof SocketTimeoutException) {
                onError(DownloadTaskListener.DOWNLOAD_ERROR_IO_ERROR_SOCKETTIMEOUTEXCEPTION);
            } else if (e instanceof UnknownHostException) {
                onError(DownloadTaskListener.DOWNLOAD_ERROR_IO_ERROR_UNKNOWNHOSTEXCEPTION);
            } else {
                onError(DownloadTaskListener.DOWNLOAD_ERROR_IO_ERROR);
            }
            BugUtil.sendBug(e);
        } finally {
            if (bis != null) try {
                bis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (inputStream != null) try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (file != null) try {
                file.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if ((toolSize == completedSize && completedSize > 100) || (completedSize > 0 && toolSize == -1)) {
            downloadStatus = DownloadStatus.DOWNLOAD_STATUS_COMPLETED;
        }
        switch (downloadStatus) {
            case DownloadStatus.DOWNLOAD_STATUS_COMPLETED:
                onCompleted();
                break;
            case DownloadStatus.DOWNLOAD_STATUS_PAUSE:
                onPause();
                break;
            case DownloadStatus.DOWNLOAD_STATUS_CANCEL:
                File temp = new File(saveDirPath, fileName);
                if (temp.exists()) temp.delete();
                onCancel();
                break;
        }
    }


    public float getPercent() {
        return completedSize * 100 / toolSize;
    }

    public void cancel() {
        downloadStatus = DownloadStatus.DOWNLOAD_STATUS_CANCEL;
        File temp = new File(saveDirPath, fileName);
        if (temp.exists()) temp.delete();
    }

    public void pause() {
        downloadStatus = DownloadStatus.DOWNLOAD_STATUS_PAUSE;
    }

    private void onPrepare() {
        for (DownloadTaskListener listener : listeners) {
            listener.onPrepare(this);
        }
    }

    private void onStart() {
        for (DownloadTaskListener listener : listeners) {
            listener.onStart(this);
        }
    }

    private void onDownloading() {
        if (listeners == null || listeners.size() == 0) {
            return;
        }
        for (DownloadTaskListener listener : listeners) {
            listener.onDownloading(this);
        }
    }

    private void onCompleted() {
        if (!alone) {
            DownloadManager.getInstance().finish(id);
        }
        if (listeners == null || listeners.size() == 0) {
            return;
        }
        for (DownloadTaskListener listener : listeners) {
            listener.onCompleted(this);
        }
    }

    private void onPause() {
        if (listeners == null || listeners.size() == 0) {
            return;
        }
        for (DownloadTaskListener listener : listeners) {
            listener.onPause(this);
        }
    }

    private void onCancel() {
        if (!alone) {
            DownloadManager.getInstance().finish(id);
        }
        if (listeners == null || listeners.size() == 0) {
            return;
        }
        for (DownloadTaskListener listener : listeners) {
            listener.onCancel(this);
        }
    }

    private void onError(int errorCode) {
        if (listeners == null || listeners.size() == 0) {
            return;
        }
        for (DownloadTaskListener listener : listeners) {
            listener.onError(this, errorCode);
        }
    }

    public void addDownloadListener(DownloadTaskListener listener) {
        if (listener != null)
            listeners.add(listener);
    }

    /**
     * if listener is null,clear all listener
     *
     * @param listener
     */
    public void removeDownloadListener(DownloadTaskListener listener) {
        if (listener == null) {
            return;
        } else {
            listeners.remove(listener);
        }
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DownloadTask)) {
            return false;
        }
        if (TextUtils.isEmpty(url) || TextUtils.isEmpty(saveDirPath)) {
            return false;
        }
        return url.equals(((DownloadTask) o).url) && saveDirPath.equals(((DownloadTask) o).saveDirPath);
    }

    public void setAlone(boolean alone) {
        this.alone = alone;
    }


    public boolean isEmpty() {
        return TextUtils.isEmpty(url) || TextUtils.isEmpty(fileName) || TextUtils.isEmpty(saveDirPath);
    }

    public Object getTag() {
        return tag;
    }

    public void setTag(Object tag) {
        this.tag = tag;
    }


    public DownloadTask copy() {
        DownloadTask task = new DownloadTask();
        task.downloadStatus = downloadStatus;
        task.url = url;
        task.saveDirPath = saveDirPath;
        task.fileName = fileName;
        task.toolSize = toolSize;
        task.completedSize = completedSize;
        task.id = id;
        return task;
    }
}
