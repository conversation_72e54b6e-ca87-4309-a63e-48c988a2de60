<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="ucrop_ImageViewWidgetIcon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:duplicateParentState">true</item>
    </style>

    <style name="ucrop_WrapperIconState">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">?attr/selectableItemBackground</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="ucrop_WrapperRotateButton">
        <item name="android:layout_width">@dimen/ucrop_size_wrapper_rotate_button</item>
        <item name="android:layout_height">@dimen/ucrop_size_wrapper_rotate_button</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="ucrop_TextViewCropAspectRatio">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/ucrop_height_crop_aspect_ratio_text</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:duplicateParentState">true</item>
        <item name="android:textColor">@color/ucrop_scale_text_view_selector</item>
    </style>

    <style name="ucrop_TextViewWidgetText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_marginTop">@dimen/ucrop_margit_top_widget_text</item>
        <item name="android:textColor">@color/ucrop_color_widget_text</item>
        <item name="android:textSize">@dimen/ucrop_text_size_widget_text</item>
    </style>

</resources>